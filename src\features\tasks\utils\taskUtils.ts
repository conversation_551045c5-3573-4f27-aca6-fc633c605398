import { Task, SubTask, RecurrencePattern, TaskPriority } from '../../../types';
import { addDays, addWeeks, addMonths, isAfter, startOfDay, isToday as isDateToday, isTomorrow as isDateTomorrow, isThisWeek as isDateThisWeek } from 'date-fns';

/**
 * Sort tasks by date and priority
 */
export function sortTasksByDateAndPriority(tasks: Task[]): Task[] {
  const priorityOrder: Record<TaskPriority, number> = {
    urgent: 4,
    high: 3,
    medium: 2,
    low: 1,
  };

  // Separate completed and pending tasks
  const pendingTasks = tasks.filter(task => task.status !== 'completed');
  const completedTasks = tasks.filter(task => task.status === 'completed');

  // Sort pending tasks by due date, then by priority
  const sortedPending = pendingTasks.sort((a, b) => {
    const dateA = new Date(a.dueDate);
    const dateB = new Date(b.dueDate);
    
    // First sort by date
    if (dateA.getTime() !== dateB.getTime()) {
      return dateA.getTime() - dateB.getTime();
    }
    
    // If dates are the same, sort by priority (higher priority first)
    return (priorityOrder[b.priority as TaskPriority] || 2) - (priorityOrder[a.priority as TaskPriority] || 2);
  });

  // Sort completed tasks by completion date (most recently completed first)
  const sortedCompleted = completedTasks.sort((a, b) => {
    return new Date(b.updatedAt || b.createdAt).getTime() - new Date(a.updatedAt || a.createdAt).getTime();
  });

  return [...sortedPending, ...sortedCompleted];
}

/**
 * Check if a task is overdue
 */
export function isOverdue(task: Task): boolean {
  if (task.status === 'completed') return false;
  return isAfter(startOfDay(new Date()), startOfDay(new Date(task.dueDate)));
}

/**
 * Check if a task is due today
 */
export function isToday(task: Task): boolean {
  return isDateToday(new Date(task.dueDate));
}

/**
 * Check if a task is due tomorrow
 */
export function isTomorrow(task: Task): boolean {
  return isDateTomorrow(new Date(task.dueDate));
}

/**
 * Check if a task is due this week
 */
export function isThisWeek(task: Task): boolean {
  return isDateThisWeek(new Date(task.dueDate));
}

/**
 * Get unique subjects from tasks
 */
export function getUniqueSubjects(tasks: Task[]): string[] {
  return tasks
    .map(task => task.subject)
    .filter(subject => subject.trim() !== '')
    .filter((subject, index, arr) => arr.indexOf(subject) === index)
    .sort();
}

/**
 * Get unique tags from tasks
 */
export function getUniqueTags(tasks: Task[]): string[] {
  const allTags = tasks
    .flatMap(task => task.tags || [])
    .filter(tag => tag.trim() !== '');
  
  return [...new Set(allTags)].sort();
}

/**
 * Generate a recurring task based on the original task
 */
export function generateRecurringTask(originalTask: Task): Omit<Task, 'id' | 'userId'> {
  if (!originalTask.recurrence) {
    throw new Error('Task does not have recurrence pattern');
  }

  const { type, interval } = originalTask.recurrence;
  let newDueDate = new Date(originalTask.dueDate);

  switch (type) {
    case 'daily':
      newDueDate = addDays(newDueDate, interval);
      break;
    case 'weekly':
      newDueDate = addWeeks(newDueDate, interval);
      break;
    case 'monthly':
      newDueDate = addMonths(newDueDate, interval);
      break;
    default:
      throw new Error(`Unsupported recurrence type: ${type}`);
  }

  return {
    ...originalTask,
    status: 'pending',
    dueDate: newDueDate.toISOString().split('T')[0],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    subtasks: originalTask.subtasks?.map(st => ({ ...st, completed: false })) || [],
  };
}

/**
 * Calculate task completion percentage
 */
export function getTaskCompletionPercentage(task: Task): number {
  if (!task.subtasks || task.subtasks.length === 0) {
    return task.status === 'completed' ? 100 : 0;
  }

  const completedSubtasks = task.subtasks.filter(st => st.completed).length;
  return Math.round((completedSubtasks / task.subtasks.length) * 100);
}

/**
 * Get priority color for UI
 */
export function getPriorityColor(priority: TaskPriority): string {
  const colors = {
    urgent: 'text-red-600 bg-red-50 border-red-200',
    high: 'text-orange-600 bg-orange-50 border-orange-200',
    medium: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    low: 'text-green-600 bg-green-50 border-green-200',
  };
  
  return colors[priority] || colors.medium;
}

/**
 * Get status color for UI
 */
export function getStatusColor(task: Task): string {
  if (task.status === 'completed') {
    return 'text-green-600 bg-green-50 border-green-200';
  }
  
  if (isOverdue(task)) {
    return 'text-red-600 bg-red-50 border-red-200';
  }
  
  if (isToday(task)) {
    return 'text-blue-600 bg-blue-50 border-blue-200';
  }
  
  return 'text-gray-600 bg-gray-50 border-gray-200';
}

/**
 * Format due date for display
 */
export function formatDueDate(dueDate: string): string {
  const date = new Date(dueDate);
  const now = new Date();
  
  if (isDateToday(date)) {
    return 'Today';
  }
  
  if (isDateTomorrow(date)) {
    return 'Tomorrow';
  }
  
  // If it's within this week
  if (isDateThisWeek(date)) {
    return date.toLocaleDateString('en-US', { weekday: 'long' });
  }
  
  // If it's this year
  if (date.getFullYear() === now.getFullYear()) {
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
  }
  
  // Full date
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });
}

/**
 * Validate task data
 */
export function validateTask(task: Partial<Task>): string[] {
  const errors: string[] = [];
  
  if (!task.title?.trim()) {
    errors.push('Title is required');
  }
  
  if (!task.dueDate) {
    errors.push('Due date is required');
  } else {
    const dueDate = new Date(task.dueDate);
    if (isNaN(dueDate.getTime())) {
      errors.push('Invalid due date');
    }
  }
  
  if (task.priority && !['low', 'medium', 'high', 'urgent'].includes(task.priority)) {
    errors.push('Invalid priority');
  }
  
  if (task.estimatedTime && (task.estimatedTime < 0 || task.estimatedTime > 24 * 60)) {
    errors.push('Estimated time must be between 0 and 1440 minutes (24 hours)');
  }
  
  return errors;
}

/**
 * Generate a unique ID for tasks
 */
export function generateTaskId(): string {
  return `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Calculate estimated completion time for a task with subtasks
 */
export function calculateTotalEstimatedTime(task: Task): number {
  let total = task.estimatedTime || 0;
  
  if (task.subtasks) {
    // Assuming each subtask takes 15 minutes if no specific time is set
    total += task.subtasks.length * 15;
  }
  
  return total;
}

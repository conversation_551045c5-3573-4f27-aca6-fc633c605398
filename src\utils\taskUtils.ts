// Utility functions for task filtering, sorting, achievement logic, etc.

import { Task, Achievement, SubTask, RecurrencePattern } from "../types";
import { addDays, addWeeks, addMonths, isAfter, startOfDay } from "date-fns";

export function sortTasksByDateAndPriority(tasks: Task[]): Task[] {
  // Separate completed and pending tasks
  const pendingTasks = tasks.filter((task) => task.status !== "completed");
  const completedTasks = tasks.filter((task) => task.status === "completed");

  // Sort pending tasks by date priority (today, tomorrow, others) then by priority
  const sortedPending = pendingTasks.sort((a, b) => {
    const today = startOfDay(new Date());
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    const aDate = startOfDay(new Date(a.dueDate));
    const bDate = startOfDay(new Date(b.dueDate));

    const aIsToday = aDate.getTime() === today.getTime();
    const aTomorrow = aDate.getTime() === tomorrow.getTime();
    const bIsToday = bDate.getTime() === today.getTime();
    const bTomorrow = bDate.getTime() === tomorrow.getTime();

    // Priority order: high = 0, medium = 1, low = 2
    const priorityOrder = { high: 0, medium: 1, low: 2 };
    const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder];
    const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder];

    // First priority: Date-based grouping (today first, tomorrow second, others last)
    if (aIsToday && !bIsToday) return -1;
    if (bIsToday && !aIsToday) return 1;
    if (aTomorrow && !bTomorrow && !bIsToday) return -1;
    if (bTomorrow && !aTomorrow && !aIsToday) return 1;

    // Within the same date group, sort by priority first
    if (aPriority !== bPriority) return aPriority - bPriority;

    // If same priority within same date group, sort by due date
    return aDate.getTime() - bDate.getTime();
  });

  // Sort completed tasks by completion date (most recently completed first)
  const sortedCompleted = completedTasks.sort((a, b) => {
    return new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
  });

  // Return pending tasks first, then completed tasks at bottom
  return [...sortedPending, ...sortedCompleted];
}

export function isOverdue(task: Task): boolean {
  if (task.status === "completed") return false;
  return isAfter(startOfDay(new Date()), startOfDay(new Date(task.dueDate)));
}

export function isToday(task: Task): boolean {
  const today = startOfDay(new Date());
  const taskDate = startOfDay(new Date(task.dueDate));
  return taskDate.getTime() === today.getTime();
}

export function isTomorrow(task: Task): boolean {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const tomorrowDate = startOfDay(tomorrow);
  const taskDate = startOfDay(new Date(task.dueDate));
  return taskDate.getTime() === tomorrowDate.getTime();
}

export function getUniqueSubjects(tasks: Task[]): string[] {
  return tasks
    .map((task) => task.subject)
    .filter((subject) => subject.trim() !== "")
    .filter((subject, index, arr) => arr.indexOf(subject) === index);
}

export function generateRecurringTask(originalTask: Task): Task {
  if (!originalTask.recurrence) return originalTask;
  const { type, interval } = originalTask.recurrence;
  let newDueDate = new Date(originalTask.dueDate);
  switch (type) {
    case "daily":
      newDueDate = addDays(newDueDate, interval);
      break;
    case "weekly":
      newDueDate = addWeeks(newDueDate, interval);
      break;
    case "monthly":
      newDueDate = addMonths(newDueDate, interval);
      break;
  }
  return {
    ...originalTask,
    id: Date.now().toString(),
    status: "pending",
    dueDate: newDueDate.toISOString().split("T")[0],
    createdAt: new Date().toISOString(),
    subtasks:
      originalTask.subtasks?.map((st) => ({ ...st, completed: false })) || [],
  };
}

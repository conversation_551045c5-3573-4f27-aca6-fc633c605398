import { useState, useCallback } from 'react';
import { Task } from '../../../types';

export interface UseBulkOperationsReturn {
  selectedTasks: Set<string>;
  isAllSelected: boolean;
  hasSelection: boolean;
  selectTask: (taskId: string) => void;
  deselectTask: (taskId: string) => void;
  toggleTask: (taskId: string) => void;
  selectAll: (tasks: Task[]) => void;
  deselectAll: () => void;
  toggleSelectAll: (tasks: Task[]) => void;
  bulkMarkCompleted: (tasks: Task[], updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>) => Promise<void>;
  bulkMarkPending: (tasks: Task[], updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>) => Promise<void>;
  bulkDelete: (tasks: Task[], deleteTask: (taskId: string) => Promise<void>) => Promise<void>;
  bulkUpdatePriority: (tasks: Task[], priority: Task['priority'], updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>) => Promise<void>;
  bulkUpdateSubject: (tasks: Task[], subject: string, updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>) => Promise<void>;
  getSelectedTasks: (tasks: Task[]) => Task[];
}

export function useBulkOperations(): UseBulkOperationsReturn {
  const [selectedTasks, setSelectedTasks] = useState<Set<string>>(new Set());

  const selectTask = useCallback((taskId: string) => {
    setSelectedTasks(prev => new Set([...prev, taskId]));
  }, []);

  const deselectTask = useCallback((taskId: string) => {
    setSelectedTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
  }, []);

  const toggleTask = useCallback((taskId: string) => {
    setSelectedTasks(prev => {
      const newSet = new Set(prev);
      if (newSet.has(taskId)) {
        newSet.delete(taskId);
      } else {
        newSet.add(taskId);
      }
      return newSet;
    });
  }, []);

  const selectAll = useCallback((tasks: Task[]) => {
    setSelectedTasks(new Set(tasks.map(task => task.id)));
  }, []);

  const deselectAll = useCallback(() => {
    setSelectedTasks(new Set());
  }, []);

  const toggleSelectAll = useCallback((tasks: Task[]) => {
    if (selectedTasks.size === tasks.length) {
      deselectAll();
    } else {
      selectAll(tasks);
    }
  }, [selectedTasks.size, selectAll, deselectAll]);

  const getSelectedTasks = useCallback((tasks: Task[]) => {
    return tasks.filter(task => selectedTasks.has(task.id));
  }, [selectedTasks]);

  const bulkMarkCompleted = useCallback(async (
    tasks: Task[],
    updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>
  ) => {
    const selectedTaskList = getSelectedTasks(tasks);
    const pendingTasks = selectedTaskList.filter(task => task.status !== 'completed');
    
    try {
      await Promise.all(
        pendingTasks.map(task => 
          updateTask(task.id, { status: 'completed' })
        )
      );
      deselectAll();
    } catch (error) {
      console.error('Error in bulk mark completed:', error);
      throw error;
    }
  }, [getSelectedTasks, deselectAll]);

  const bulkMarkPending = useCallback(async (
    tasks: Task[],
    updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>
  ) => {
    const selectedTaskList = getSelectedTasks(tasks);
    const completedTasks = selectedTaskList.filter(task => task.status === 'completed');
    
    try {
      await Promise.all(
        completedTasks.map(task => 
          updateTask(task.id, { status: 'pending' })
        )
      );
      deselectAll();
    } catch (error) {
      console.error('Error in bulk mark pending:', error);
      throw error;
    }
  }, [getSelectedTasks, deselectAll]);

  const bulkDelete = useCallback(async (
    tasks: Task[],
    deleteTask: (taskId: string) => Promise<void>
  ) => {
    const selectedTaskList = getSelectedTasks(tasks);
    
    if (selectedTaskList.length === 0) return;

    const confirmMessage = `Are you sure you want to delete ${selectedTaskList.length} ${
      selectedTaskList.length === 1 ? 'task' : 'tasks'
    }? This action cannot be undone.`;

    if (!window.confirm(confirmMessage)) return;

    try {
      await Promise.all(
        selectedTaskList.map(task => deleteTask(task.id))
      );
      deselectAll();
    } catch (error) {
      console.error('Error in bulk delete:', error);
      throw error;
    }
  }, [getSelectedTasks, deselectAll]);

  const bulkUpdatePriority = useCallback(async (
    tasks: Task[],
    priority: Task['priority'],
    updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>
  ) => {
    const selectedTaskList = getSelectedTasks(tasks);
    
    try {
      await Promise.all(
        selectedTaskList.map(task => 
          updateTask(task.id, { priority })
        )
      );
      deselectAll();
    } catch (error) {
      console.error('Error in bulk update priority:', error);
      throw error;
    }
  }, [getSelectedTasks, deselectAll]);

  const bulkUpdateSubject = useCallback(async (
    tasks: Task[],
    subject: string,
    updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>
  ) => {
    const selectedTaskList = getSelectedTasks(tasks);
    
    try {
      await Promise.all(
        selectedTaskList.map(task => 
          updateTask(task.id, { subject })
        )
      );
      deselectAll();
    } catch (error) {
      console.error('Error in bulk update subject:', error);
      throw error;
    }
  }, [getSelectedTasks, deselectAll]);

  const isAllSelected = useCallback((tasks: Task[]) => {
    return tasks.length > 0 && selectedTasks.size === tasks.length;
  }, [selectedTasks.size]);

  const hasSelection = selectedTasks.size > 0;

  return {
    selectedTasks,
    isAllSelected: isAllSelected,
    hasSelection,
    selectTask,
    deselectTask,
    toggleTask,
    selectAll,
    deselectAll,
    toggleSelectAll,
    bulkMarkCompleted,
    bulkMarkPending,
    bulkDelete,
    bulkUpdatePriority,
    bulkUpdateSubject,
    getSelectedTasks,
  };
}

import React from "react";
import { Trophy, X } from "lucide-react";
import { format } from "date-fns";
import { Achievement } from "../types";

interface AchievementsPanelProps {
  achievements: Achievement[];
  onClose: () => void;
}

const AchievementsPanel: React.FC<AchievementsPanelProps> = ({
  achievements,
  onClose,
}) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div className="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] overflow-hidden">
      <div className="p-6 border-b border-gray-200 flex items-center justify-between">
        <h3 className="text-xl font-semibold flex items-center">
          <Trophy className="w-6 h-6 mr-2 text-yellow-600" />
          Achievements
        </h3>
        <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
          <X className="w-6 h-6" />
        </button>
      </div>
      <div className="p-6 overflow-y-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {achievements.map((achievement) => (
            <div
              key={achievement.id}
              className={`p-4 rounded-lg border-2 transition-all duration-300 ${
                achievement.unlocked
                  ? "bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-300 shadow-lg"
                  : "bg-gray-50 border-gray-200"
              }`}
            >
              <div className="flex items-start space-x-3">
                <div
                  className={`text-3xl ${
                    achievement.unlocked ? "grayscale-0" : "grayscale"
                  }`}
                >
                  {achievement.icon}
                </div>
                <div className="flex-1">
                  <h4
                    className={`font-semibold ${
                      achievement.unlocked ? "text-yellow-800" : "text-gray-600"
                    }`}
                  >
                    {achievement.name}
                  </h4>
                  <p
                    className={`text-sm ${
                      achievement.unlocked ? "text-yellow-700" : "text-gray-500"
                    }`}
                  >
                    {achievement.description}
                  </p>
                  {achievement.target && achievement.target > 1 && (
                    <div className="mt-2">
                      <div className="flex justify-between text-xs mb-1">
                        <span>Progress</span>
                        <span>
                          {achievement.progress || 0}/{achievement.target}
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full transition-all duration-300 ${
                            achievement.unlocked
                              ? "bg-yellow-600"
                              : "bg-gray-400"
                          }`}
                          style={{
                            width: `${Math.min(
                              ((achievement.progress || 0) /
                                achievement.target) *
                                100,
                              100
                            )}%`,
                          }}
                        ></div>
                      </div>
                    </div>
                  )}
                  {achievement.unlocked && achievement.unlockedAt && (
                    <p className="text-xs text-yellow-600 mt-2">
                      🏆 Unlocked{" "}
                      {format(new Date(achievement.unlockedAt), "MMM dd, yyyy")}
                    </p>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  </div>
);

export default AchievementsPanel;

import React from 'react';
import { CheckCircle2, Clock, AlertTriangle, TrendingUp, Calendar } from 'lucide-react';
import { TaskStats as TaskStatsType, Task } from '../../../types';
import { formatDueDate } from '../utils/taskUtils';

interface TaskStatsProps {
  stats: TaskStatsType;
  completionRate: number;
  overdueCount: number;
  upcomingDeadlines: Task[];
  className?: string;
}

export const TaskStats: React.FC<TaskStatsProps> = ({
  stats,
  completionRate,
  overdueCount,
  upcomingDeadlines,
  className = '',
}) => {
  const statCards = [
    {
      title: 'Total Tasks',
      value: stats.total,
      icon: CheckCircle2,
      color: 'text-blue-600 bg-blue-50 border-blue-200',
    },
    {
      title: 'Completed',
      value: stats.completed,
      icon: CheckCircle2,
      color: 'text-green-600 bg-green-50 border-green-200',
    },
    {
      title: 'Pending',
      value: stats.pending,
      icon: Clock,
      color: 'text-yellow-600 bg-yellow-50 border-yellow-200',
    },
    {
      title: 'Overdue',
      value: stats.overdue,
      icon: AlertTriangle,
      color: 'text-red-600 bg-red-50 border-red-200',
    },
  ];

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Main Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {statCards.map(({ title, value, icon: Icon, color }) => (
          <div
            key={title}
            className={`p-4 rounded-lg border ${color}`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium opacity-75">{title}</p>
                <p className="text-2xl font-bold">{value}</p>
              </div>
              <Icon className="w-8 h-8 opacity-75" />
            </div>
          </div>
        ))}
      </div>

      {/* Additional Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        {/* Completion Rate */}
        <div className="p-4 bg-white border border-gray-200 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">Completion Rate</h4>
            <TrendingUp className="w-4 h-4 text-gray-400" />
          </div>
          <div className="flex items-center gap-3">
            <div className="flex-1">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-green-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${completionRate}%` }}
                />
              </div>
            </div>
            <span className="text-lg font-semibold text-gray-900">
              {completionRate}%
            </span>
          </div>
        </div>

        {/* Today's Tasks */}
        <div className="p-4 bg-white border border-gray-200 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">Today</h4>
            <Calendar className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {stats.today}
          </div>
          <p className="text-xs text-gray-600 mt-1">
            {stats.today === 1 ? 'task due' : 'tasks due'}
          </p>
        </div>

        {/* This Week */}
        <div className="p-4 bg-white border border-gray-200 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium text-gray-700">This Week</h4>
            <Calendar className="w-4 h-4 text-gray-400" />
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {stats.thisWeek}
          </div>
          <p className="text-xs text-gray-600 mt-1">
            {stats.thisWeek === 1 ? 'task due' : 'tasks due'}
          </p>
        </div>
      </div>

      {/* Upcoming Deadlines */}
      {upcomingDeadlines.length > 0 && (
        <div className="p-4 bg-white border border-gray-200 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-3 flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Upcoming Deadlines
          </h4>
          <div className="space-y-2">
            {upcomingDeadlines.slice(0, 3).map((task) => (
              <div
                key={task.id}
                className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded-lg"
              >
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {task.title}
                  </p>
                  {task.subject && (
                    <p className="text-xs text-gray-600">{task.subject}</p>
                  )}
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-700">
                    {formatDueDate(task.dueDate)}
                  </p>
                  <span
                    className={`inline-block px-2 py-0.5 text-xs rounded-full ${
                      task.priority === 'urgent'
                        ? 'bg-red-100 text-red-800'
                        : task.priority === 'high'
                        ? 'bg-orange-100 text-orange-800'
                        : task.priority === 'medium'
                        ? 'bg-yellow-100 text-yellow-800'
                        : 'bg-green-100 text-green-800'
                    }`}
                  >
                    {task.priority}
                  </span>
                </div>
              </div>
            ))}
            {upcomingDeadlines.length > 3 && (
              <p className="text-xs text-gray-600 text-center pt-2">
                +{upcomingDeadlines.length - 3} more upcoming
              </p>
            )}
          </div>
        </div>
      )}

      {/* Overdue Alert */}
      {overdueCount > 0 && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <div>
              <h4 className="text-sm font-medium text-red-800">
                {overdueCount} {overdueCount === 1 ? 'Task' : 'Tasks'} Overdue
              </h4>
              <p className="text-xs text-red-700">
                Please review and update your overdue tasks.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

import { useEffect, useCallback } from 'react';

export interface KeyboardShortcuts {
  onNewTask: () => void;
  onSearch: () => void;
  onToggleFilters: () => void;
  onRefresh: () => void;
  onSelectAll: () => void;
  onDeleteSelected: () => void;
  onMarkCompleted: () => void;
  onEscape: () => void;
}

export function useKeyboardShortcuts(shortcuts: Partial<KeyboardShortcuts>) {
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in inputs
    if (
      event.target instanceof HTMLInputElement ||
      event.target instanceof HTMLTextAreaElement ||
      event.target instanceof HTMLSelectElement ||
      (event.target as HTMLElement)?.contentEditable === 'true'
    ) {
      // Allow Escape to work in inputs
      if (event.key === 'Escape' && shortcuts.onEscape) {
        shortcuts.onEscape();
      }
      return;
    }

    const { key, ctrlKey, metaKey, shiftKey } = event;
    const isModifierPressed = ctrlKey || metaKey;

    // Prevent default for our shortcuts
    let shouldPreventDefault = false;

    switch (key) {
      case 'n':
      case 'N':
        if (isModifierPressed && shortcuts.onNewTask) {
          shortcuts.onNewTask();
          shouldPreventDefault = true;
        }
        break;

      case 'f':
      case 'F':
        if (isModifierPressed && shortcuts.onSearch) {
          shortcuts.onSearch();
          shouldPreventDefault = true;
        }
        break;

      case 'k':
      case 'K':
        if (isModifierPressed && shortcuts.onSearch) {
          shortcuts.onSearch();
          shouldPreventDefault = true;
        }
        break;

      case 'r':
      case 'R':
        if (isModifierPressed && shortcuts.onRefresh) {
          shortcuts.onRefresh();
          shouldPreventDefault = true;
        }
        break;

      case 'a':
      case 'A':
        if (isModifierPressed && shortcuts.onSelectAll) {
          shortcuts.onSelectAll();
          shouldPreventDefault = true;
        }
        break;

      case 'Delete':
      case 'Backspace':
        if (shortcuts.onDeleteSelected) {
          shortcuts.onDeleteSelected();
          shouldPreventDefault = true;
        }
        break;

      case 'Enter':
        if (isModifierPressed && shortcuts.onMarkCompleted) {
          shortcuts.onMarkCompleted();
          shouldPreventDefault = true;
        }
        break;

      case 'Escape':
        if (shortcuts.onEscape) {
          shortcuts.onEscape();
          shouldPreventDefault = true;
        }
        break;

      case '?':
        if (shiftKey) {
          // Show keyboard shortcuts help
          showKeyboardShortcutsHelp();
          shouldPreventDefault = true;
        }
        break;

      case 'g':
      case 'G':
        if (isModifierPressed && shortcuts.onToggleFilters) {
          shortcuts.onToggleFilters();
          shouldPreventDefault = true;
        }
        break;
    }

    if (shouldPreventDefault) {
      event.preventDefault();
    }
  }, [shortcuts]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
}

function showKeyboardShortcutsHelp() {
  const shortcuts = [
    { key: 'Ctrl/Cmd + N', description: 'Create new task' },
    { key: 'Ctrl/Cmd + F', description: 'Focus search' },
    { key: 'Ctrl/Cmd + K', description: 'Focus search (alternative)' },
    { key: 'Ctrl/Cmd + R', description: 'Refresh tasks' },
    { key: 'Ctrl/Cmd + A', description: 'Select all tasks' },
    { key: 'Ctrl/Cmd + G', description: 'Toggle filters' },
    { key: 'Ctrl/Cmd + Enter', description: 'Mark selected as completed' },
    { key: 'Delete/Backspace', description: 'Delete selected tasks' },
    { key: 'Escape', description: 'Clear selection / Close modals' },
    { key: 'Shift + ?', description: 'Show this help' },
  ];

  const helpContent = shortcuts
    .map(({ key, description }) => `${key}: ${description}`)
    .join('\n');

  alert(`Keyboard Shortcuts:\n\n${helpContent}`);
}

import { useState, useEffect, useCallback } from 'react';
import { Task, Achievement } from '../../../types';
import { achievementService } from '../services/achievementService';

export interface UseTaskAchievementsReturn {
  achievements: Achievement[];
  unlockedAchievements: Achievement[];
  checkAchievements: (tasks: Task[]) => Promise<Achievement[]>;
  markAchievementSeen: (achievementId: string) => void;
  getProgress: (achievementId: string) => number;
}

export function useTaskAchievements(tasks: Task[]): UseTaskAchievementsReturn {
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [unlockedAchievements, setUnlockedAchievements] = useState<Achievement[]>([]);

  // Initialize achievements
  useEffect(() => {
    const initialAchievements = achievementService.getAchievements();
    setAchievements(initialAchievements);
  }, []);

  const checkAchievements = useCallback(async (currentTasks: Task[]): Promise<Achievement[]> => {
    const newlyUnlocked = await achievementService.checkAchievements(currentTasks);
    
    if (newlyUnlocked.length > 0) {
      setAchievements(prev => 
        prev.map(achievement => {
          const unlocked = newlyUnlocked.find(a => a.id === achievement.id);
          return unlocked || achievement;
        })
      );
      
      setUnlockedAchievements(prev => [...prev, ...newlyUnlocked]);
    }
    
    return newlyUnlocked;
  }, []);

  const markAchievementSeen = useCallback((achievementId: string) => {
    setUnlockedAchievements(prev => 
      prev.filter(achievement => achievement.id !== achievementId)
    );
  }, []);

  const getProgress = useCallback((achievementId: string): number => {
    const achievement = achievements.find(a => a.id === achievementId);
    if (!achievement || !achievement.target) return 0;
    
    return Math.min(100, Math.round(((achievement.progress || 0) / achievement.target) * 100));
  }, [achievements]);

  // Check achievements whenever tasks change
  useEffect(() => {
    if (tasks.length > 0) {
      checkAchievements(tasks);
    }
  }, [tasks, checkAchievements]);

  return {
    achievements,
    unlockedAchievements,
    checkAchievements,
    markAchievementSeen,
    getProgress,
  };
}

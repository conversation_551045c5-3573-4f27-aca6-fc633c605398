import React from "react";
import { Task } from "../types";

interface TaskItemProps {
  task: Task;
  onEdit: (task: Task) => void;
  onDelete: (taskId: string) => void;
  onToggle: (taskId: string) => void;
}

const TaskItem: React.FC<TaskItemProps> = ({
  task,
  onEdit,
  onDelete,
  onToggle,
}) => {
  return (
    <div>
      {/* Render single task details here */}
      {/* ...existing code... */}
    </div>
  );
};

export default TaskItem;

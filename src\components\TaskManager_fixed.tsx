import React, { useState, useEffect } from "react";
import {
  Plus,
  Calendar,
  CheckCircle2,
  Circle,
  Edit3,
  Trash2,
  X,
  Trophy,
  Repeat,
  ChevronDown,
  ChevronRight,
} from "lucide-react";
import { Task, SubTask, Achievement, RecurrencePattern } from "../types";
import { format } from "date-fns";

const TaskManager: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [showAddTask, setShowAddTask] = useState(false);
  const [animatingTasks, setAnimatingTasks] = useState<Set<string>>(new Set());

  // Enhanced features state
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [showAchievements, setShowAchievements] = useState(false);
  const [expandedTasks, setExpandedTasks] = useState<Set<string>>(new Set());
  const [newTask, setNewTask] = useState<
    Partial<Task & { recurrence?: RecurrencePattern; subtasks?: SubTask[] }>
  >({
    title: "",
    description: "",
    priority: "medium",
    subject: "",
    dueDate: format(new Date(), "yyyy-MM-dd"),
    subtasks: [],
  });

  const user = { uid: "demo-user" }; // Demo user for now

  // Initialize achievements
  const initializeAchievements = (): Achievement[] => [
    {
      id: "first-task",
      name: "Getting Started",
      description: "Create your first task",
      icon: "🎯",
      unlocked: false,
      target: 1,
      progress: 0,
      condition: "tasks_created",
    },
    {
      id: "task-master",
      name: "Task Master",
      description: "Complete 10 tasks",
      icon: "🏆",
      unlocked: false,
      target: 10,
      progress: 0,
      condition: "tasks_completed",
    },
    // Add more achievements...
  ];

  useEffect(() => {
    if (achievements.length === 0) {
      setAchievements(initializeAchievements());
    }
  }, []);

  // Enhanced Task Item Component
  const EnhancedTaskItem: React.FC<{
    task: Task;
  }> = ({ task }) => {
    const isExpanded = expandedTasks.has(task.id);
    const hasSubtasks = task.subtasks && task.subtasks.length > 0;
    const completedSubtasksCount =
      task.subtasks?.filter((st) => st.completed).length || 0;
    const totalSubtasksCount = task.subtasks?.length || 0;
    const progressPercent =
      totalSubtasksCount > 0
        ? (completedSubtasksCount / totalSubtasksCount) * 100
        : 0;

    return (
      <div
        className={`transform transition-all duration-300 ${
          animatingTasks.has(task.id)
            ? "animate-task-rocket pointer-events-none border-2 border-blue-400 shadow-rocket bg-gradient-to-t from-blue-100 via-white to-blue-300"
            : task.status === "completed"
            ? "bg-gradient-to-r from-gray-50 to-gray-100 border-gray-300 shadow-sm opacity-60 scale-95"
            : "bg-gradient-to-r from-white to-gray-50 border-gray-200 shadow-lg hover:shadow-xl"
        } border rounded-xl p-5 hover:border-blue-200 backdrop-blur-sm`}
      >
        <div className="flex items-start space-x-3">
          <button
            onClick={() => toggleTaskStatus(task)}
            className={`mt-1 p-1 rounded-full transition-all duration-300 transform ${
              task.status === "completed"
                ? "text-green-600 hover:text-green-700 hover:bg-green-50 scale-110 animate-pulse"
                : "text-blue-600 hover:text-blue-700 hover:bg-blue-50 hover:scale-110"
            }`}
          >
            {task.status === "completed" ? (
              <CheckCircle2 className="w-6 h-6" />
            ) : (
              <Circle className="w-6 h-6" />
            )}
          </button>

          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2">
                  <h3
                    className={`font-medium ${
                      task.status === "completed"
                        ? "text-gray-500 line-through"
                        : "text-gray-900"
                    }`}
                  >
                    {task.title}
                  </h3>

                  {task.recurrence && (
                    <div title="Recurring Task">
                      <Repeat className="w-4 h-4 text-blue-500" />
                    </div>
                  )}

                  {hasSubtasks && (
                    <button
                      onClick={() => {
                        const newExpanded = new Set(expandedTasks);
                        if (isExpanded) {
                          newExpanded.delete(task.id);
                        } else {
                          newExpanded.add(task.id);
                        }
                        setExpandedTasks(newExpanded);
                      }}
                      className="text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      {isExpanded ? (
                        <ChevronDown className="w-4 h-4" />
                      ) : (
                        <ChevronRight className="w-4 h-4" />
                      )}
                    </button>
                  )}
                </div>

                {task.description && (
                  <p className="text-sm mt-1 text-gray-600">
                    {task.description}
                  </p>
                )}

                {hasSubtasks && (
                  <div className="mt-2">
                    <div className="flex items-center justify-between text-xs text-gray-500 mb-1">
                      <span>Subtasks Progress</span>
                      <span>
                        {completedSubtasksCount}/{totalSubtasksCount}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${progressPercent}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

              <div className="flex items-center space-x-2 ml-4">
                <button
                  onClick={() => {
                    /* Edit functionality */
                  }}
                  className="p-2 rounded-full text-gray-400 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 hover:scale-105"
                >
                  <Edit3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => deleteTask(task.id)}
                  className="p-2 rounded-full text-gray-400 hover:text-red-600 hover:bg-red-50 transition-all duration-200 hover:scale-105"
                >
                  <Trash2 className="w-4 h-4" />
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-4 mt-3">
              {task.subject && (
                <span className="px-2 py-1 bg-blue-100 text-blue-600 text-xs rounded-full">
                  {task.subject}
                </span>
              )}
              <span className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                {task.priority} priority
              </span>
              <div className="flex items-center text-xs text-gray-500">
                <Calendar className="w-3 h-3 mr-1" />
                {format(new Date(task.dueDate), "MMM dd, yyyy")}
              </div>
            </div>

            {hasSubtasks && isExpanded && (
              <div className="mt-4 pl-4 border-l-2 border-gray-200">
                <div className="space-y-2">
                  {task.subtasks!.map((subtask) => (
                    <div
                      key={subtask.id}
                      className="flex items-center space-x-2"
                    >
                      <button
                        onClick={() => toggleSubtask(task.id, subtask.id)}
                        className="text-gray-400 hover:text-blue-600 transition-colors"
                      >
                        {subtask.completed ? (
                          <CheckCircle2 className="w-4 h-4 text-green-600" />
                        ) : (
                          <Circle className="w-4 h-4" />
                        )}
                      </button>
                      <span
                        className={`text-sm ${
                          subtask.completed
                            ? "text-gray-500 line-through"
                            : "text-gray-700"
                        }`}
                      >
                        {subtask.title}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Basic functions (simplified for demo)
  const toggleTaskStatus = async (task: Task) => {
    const updatedTask = {
      ...task,
      status: task.status === "completed" ? "pending" : "completed",
    } as Task;

    setAnimatingTasks((prev) => new Set([...prev, task.id]));

    setTimeout(() => {
      setTasks((prev) => prev.map((t) => (t.id === task.id ? updatedTask : t)));
      setAnimatingTasks((prev) => {
        const newSet = new Set(prev);
        newSet.delete(task.id);
        return newSet;
      });
    }, 1800);

    if (updatedTask.status === "completed") {
      // Task completed successfully
    }
  };

  const deleteTask = (taskId: string) => {
    setTasks((prev) => prev.filter((task) => task.id !== taskId));
  };

  const toggleSubtask = async (taskId: string, subtaskId: string) => {
    setTasks((prev) =>
      prev.map((task) => {
        if (task.id === taskId && task.subtasks) {
          return {
            ...task,
            subtasks: task.subtasks.map((subtask) =>
              subtask.id === subtaskId
                ? { ...subtask, completed: !subtask.completed }
                : subtask
            ),
          };
        }
        return task;
      })
    );
  };

  const addTask = () => {
    const task: Task = {
      id: Date.now().toString(),
      userId: user?.uid || "demo-user",
      title: newTask.title || "New Task",
      description: newTask.description || "",
      priority: (newTask.priority as "low" | "medium" | "high") || "medium",
      subject: newTask.subject || "",
      dueDate: newTask.dueDate || format(new Date(), "yyyy-MM-dd"),
      status: "pending",
      createdAt: new Date().toISOString(),
      subtasks: newTask.subtasks || [],
      recurrence: newTask.recurrence,
      order: tasks.length,
    };

    setTasks((prev) => [...prev, task]);
    setNewTask({
      title: "",
      description: "",
      priority: "medium",
      subject: "",
      dueDate: format(new Date(), "yyyy-MM-dd"),
      subtasks: [],
    });
    setShowAddTask(false);
  };

  if (!user) {
    return (
      <div className="p-6 text-center">
        Please sign in to manage your tasks.
      </div>
    );
  }

  return (
    <>
      <style>{`
        @keyframes task-rocket {
          0% { transform: scale(1) translateY(0) rotate(0deg); opacity: 1; }
          100% { transform: scale(1.4) translateY(-400px) rotate(0deg); opacity: 0; }
        }
        .animate-task-rocket { animation: task-rocket 1.8s ease-out forwards; }
        .shadow-rocket { box-shadow: 0 8px 32px rgba(0, 234, 255, 0.5); }
      `}</style>

      <div className="bg-white h-full flex flex-col">
        {/* Header */}
        <div className="border-b border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-4">
              <h2 className="text-2xl font-bold text-gray-900">
                Enhanced Task Manager
              </h2>
              <button
                onClick={() => setShowAchievements(true)}
                className="flex items-center px-3 py-1.5 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors text-sm"
              >
                <Trophy className="w-4 h-4 mr-1" />
                Achievements ({achievements.filter((a) => a.unlocked).length}/
                {achievements.length})
              </button>
            </div>
            <button
              onClick={() => setShowAddTask(true)}
              className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </button>
          </div>
        </div>

        {/* Task List */}
        <div className="flex-1 overflow-y-auto p-6">
          {tasks.length === 0 ? (
            <div className="text-center py-12">
              <CheckCircle2 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No tasks yet
              </h3>
              <p className="text-gray-600 mb-6">
                Create your first task to get started
              </p>
            </div>
          ) : (
            <div className="space-y-3">
              {tasks.map((task) => (
                <EnhancedTaskItem key={task.id} task={task} />
              ))}
            </div>
          )}
        </div>

        {/* Add Task Modal */}
        {showAddTask && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-md p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Add New Task</h3>
                <button
                  onClick={() => setShowAddTask(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <div className="space-y-4">
                <input
                  type="text"
                  placeholder="Task title..."
                  value={newTask.title}
                  onChange={(e) =>
                    setNewTask({ ...newTask, title: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />

                <textarea
                  placeholder="Description..."
                  value={newTask.description}
                  onChange={(e) =>
                    setNewTask({ ...newTask, description: e.target.value })
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  rows={3}
                />

                <div className="flex space-x-3 pt-4">
                  <button
                    onClick={() => setShowAddTask(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={addTask}
                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Add Task
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Achievements Modal */}
        {showAchievements && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-2xl p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-xl font-semibold">Achievements</h3>
                <button
                  onClick={() => setShowAchievements(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {achievements.map((achievement) => (
                  <div key={achievement.id} className="p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{achievement.icon}</span>
                      <div>
                        <h4 className="font-semibold">{achievement.name}</h4>
                        <p className="text-sm text-gray-600">
                          {achievement.description}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default TaskManager;

import { useState, useCallback } from 'react';

export interface ErrorState {
  message: string;
  code?: string;
  details?: any;
  timestamp: number;
}

export interface UseErrorHandlerReturn {
  error: ErrorState | null;
  setError: (error: string | Error | ErrorState) => void;
  clearError: () => void;
  handleAsyncError: <T>(promise: Promise<T>) => Promise<T>;
  withErrorHandling: <T extends any[], R>(
    fn: (...args: T) => Promise<R>
  ) => (...args: T) => Promise<R | undefined>;
}

export function useErrorHandler(): UseErrorHandlerReturn {
  const [error, setErrorState] = useState<ErrorState | null>(null);

  const setError = useCallback((error: string | Error | ErrorState) => {
    let errorState: ErrorState;

    if (typeof error === 'string') {
      errorState = {
        message: error,
        timestamp: Date.now(),
      };
    } else if (error instanceof Error) {
      errorState = {
        message: error.message,
        code: error.name,
        details: {
          stack: error.stack,
        },
        timestamp: Date.now(),
      };
    } else {
      errorState = {
        ...error,
        timestamp: Date.now(),
      };
    }

    setErrorState(errorState);

    // Log error for debugging
    console.error('Error occurred:', errorState);
  }, []);

  const clearError = useCallback(() => {
    setErrorState(null);
  }, []);

  const handleAsyncError = useCallback(async <T>(promise: Promise<T>): Promise<T> => {
    try {
      return await promise;
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  }, [setError]);

  const withErrorHandling = useCallback(<T extends any[], R>(
    fn: (...args: T) => Promise<R>
  ) => {
    return async (...args: T): Promise<R | undefined> => {
      try {
        return await fn(...args);
      } catch (err) {
        setError(err as Error);
        return undefined;
      }
    };
  }, [setError]);

  return {
    error,
    setError,
    clearError,
    handleAsyncError,
    withErrorHandling,
  };
}

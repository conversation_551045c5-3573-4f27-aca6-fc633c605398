export interface User {
  id: string;
  username: string;
  email: string;
  createdAt: string;
}

export interface AuthToken {
  token: string;
  user: User;
  expiresAt: string;
}

export interface FileItem {
  id: string;
  name: string;
  type: "file" | "folder";
  mimeType?: string;
  size?: number;
  parentId?: string;
  content?: string; // Base64 for files
  uploadedAt: string;
  userId: string;
}

export interface SubTask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: string;
}

export interface RecurrencePattern {
  type: "daily" | "weekly" | "monthly";
  interval: number;
  endDate?: string;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  condition: string;
  unlocked: boolean;
  unlockedAt?: string;
  progress?: number;
  target?: number;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  subject: string;
  dueDate: string;
  priority: "low" | "medium" | "high";
  status: "pending" | "completed";
  userId: string;
  createdAt: string;
  updatedAt?: string;
  // New features
  parentId?: string; // For subtasks
  subtasks?: SubTask[];
  recurrence?: RecurrencePattern;
  order?: number; // For drag & drop ordering
  completedSubtasks?: number;
  totalSubtasks?: number;
  tags?: string[];
  estimatedTime?: number; // in minutes
  actualTime?: number; // in minutes
  attachments?: string[];
}

// Enhanced types for better type safety
export type TaskStatus = "pending" | "completed" | "in-progress" | "cancelled";
export type TaskPriority = "low" | "medium" | "high" | "urgent";
export type TaskFilter =
  | "all"
  | "pending"
  | "completed"
  | "overdue"
  | "today"
  | "tomorrow"
  | "this-week";

export interface TaskFormData {
  title: string;
  description: string;
  subject: string;
  dueDate: string;
  priority: TaskPriority;
  subtasks: SubTask[];
  recurrence: RecurrencePattern | null;
  tags: string[];
  estimatedTime?: number;
}

export interface TaskFilters {
  status: TaskFilter;
  priority: TaskPriority | "all";
  subject: string;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  search: string;
  tags: string[];
}

export interface TaskStats {
  total: number;
  pending: number;
  completed: number;
  overdue: number;
  today: number;
  tomorrow: number;
  thisWeek: number;
}

export interface Note {
  id: string;
  title: string;
  content: string;
  documentId?: string;
  pageNumber?: number;
  tags: string[];
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface AIAnalysis {
  id: string;
  documentId: string;
  extractedText: string;
  embeddings?: number[];
  concepts: string[];
  summary: string;
  userId: string;
  createdAt: string;
}

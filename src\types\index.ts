export interface User {
  id: string;
  username: string;
  email: string;
  createdAt: string;
}

export interface AuthToken {
  token: string;
  user: User;
  expiresAt: string;
}

export interface FileItem {
  id: string;
  name: string;
  type: "file" | "folder";
  mimeType?: string;
  size?: number;
  parentId?: string;
  content?: string; // Base64 for files
  uploadedAt: string;
  userId: string;
}

export interface SubTask {
  id: string;
  title: string;
  completed: boolean;
  createdAt: string;
}

export interface RecurrencePattern {
  type: "daily" | "weekly" | "monthly";
  interval: number;
  endDate?: string;
}

export interface Achievement {
  id: string;
  name: string;
  description: string;
  icon: string;
  condition: string;
  unlocked: boolean;
  unlockedAt?: string;
  progress?: number;
  target?: number;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  subject: string;
  dueDate: string;
  priority: "low" | "medium" | "high";
  status: "pending" | "completed";
  userId: string;
  createdAt: string;
  // New features
  parentId?: string; // For subtasks
  subtasks?: SubTask[];
  recurrence?: RecurrencePattern;
  order?: number; // For drag & drop ordering
  completedSubtasks?: number;
  totalSubtasks?: number;
}

export interface Note {
  id: string;
  title: string;
  content: string;
  documentId?: string;
  pageNumber?: number;
  tags: string[];
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface AIAnalysis {
  id: string;
  documentId: string;
  extractedText: string;
  embeddings?: number[];
  concepts: string[];
  summary: string;
  userId: string;
  createdAt: string;
}

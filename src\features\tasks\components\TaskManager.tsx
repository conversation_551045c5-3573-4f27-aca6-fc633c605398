import React, { useState } from 'react';
import { Plus, Filter, Search, Download, Upload } from 'lucide-react';
import { useTasks } from '../hooks/useTasks';
import { useTaskFilters } from '../hooks/useTaskFilters';
import { useTaskStats } from '../hooks/useTaskStats';
import { useTaskAchievements } from '../hooks/useTaskAchievements';
import { TaskList } from './TaskList';
import { TaskForm } from './TaskForm';
import { TaskFilters } from './TaskFilters';
import { TaskStats } from './TaskStats';
import { AchievementNotification } from './AchievementNotification';
import { LoadingSpinner } from '../../../components/ui/LoadingSpinner';
import { ErrorMessage } from '../../../components/ui/ErrorMessage';
import { realTimeAuth } from '../../../utils/realTimeAuth';

export const TaskManager: React.FC = () => {
  const [showTaskForm, setShowTaskForm] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [editingTask, setEditingTask] = useState(null);

  const user = realTimeAuth.getCurrentUser();
  
  const {
    tasks,
    loading,
    error,
    createTask,
    updateTask,
    deleteTask,
    toggleTaskStatus,
    refreshTasks,
    clearError,
  } = useTasks();

  const {
    filters,
    filteredTasks,
    updateFilter,
    clearFilters,
    setSearchQuery,
    getFilterCounts,
  } = useTaskFilters(tasks);

  const {
    stats,
    completionRate,
    upcomingDeadlines,
    overdueCount,
  } = useTaskStats(tasks);

  const {
    achievements,
    unlockedAchievements,
    markAchievementSeen,
  } = useTaskAchievements(tasks);

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <h2 className="text-2xl font-bold mb-4">
          Please log in to access your tasks.
        </h2>
        <p className="text-gray-600 mb-6">
          Sign in to create, view, and manage your personal cloud tasks.
        </p>
      </div>
    );
  }

  const handleCreateTask = async (taskData) => {
    try {
      await createTask(taskData);
      setShowTaskForm(false);
    } catch (error) {
      console.error('Error creating task:', error);
    }
  };

  const handleUpdateTask = async (taskData) => {
    if (!editingTask) return;
    
    try {
      await updateTask(editingTask.id, taskData);
      setEditingTask(null);
      setShowTaskForm(false);
    } catch (error) {
      console.error('Error updating task:', error);
    }
  };

  const handleEditTask = (task) => {
    setEditingTask(task);
    setShowTaskForm(true);
  };

  const handleDeleteTask = async (taskId) => {
    if (window.confirm('Are you sure you want to delete this task?')) {
      try {
        await deleteTask(taskId);
      } catch (error) {
        console.error('Error deleting task:', error);
      }
    }
  };

  const handleExportTasks = () => {
    try {
      const dataStr = JSON.stringify(tasks, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `tasks-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Error exporting tasks:', error);
    }
  };

  const handleImportTasks = (event) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const content = e.target?.result as string;
        // TODO: Implement import functionality
        console.log('Import content:', content);
      } catch (error) {
        console.error('Error importing tasks:', error);
      }
    };
    reader.readAsText(file);
  };

  return (
    <div className="bg-white h-full flex flex-col">
      {/* Header */}
      <div className="border-b border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Task Manager</h2>
            <p className="text-sm text-gray-600 mt-1">
              {filteredTasks.length === tasks.length
                ? `${tasks.length} ${tasks.length === 1 ? 'task' : 'tasks'} total`
                : `${filteredTasks.length} of ${tasks.length} tasks shown`}
            </p>
          </div>
          
          <div className="flex items-center gap-3">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search tasks..."
                value={filters.search}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            {/* Filter Toggle */}
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`p-2 rounded-lg border transition-colors ${
                showFilters
                  ? 'bg-blue-50 border-blue-200 text-blue-600'
                  : 'border-gray-300 text-gray-600 hover:bg-gray-50'
              }`}
            >
              <Filter className="w-4 h-4" />
            </button>

            {/* Export */}
            <button
              onClick={handleExportTasks}
              className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 transition-colors"
              title="Export tasks"
            >
              <Download className="w-4 h-4" />
            </button>

            {/* Import */}
            <label className="p-2 rounded-lg border border-gray-300 text-gray-600 hover:bg-gray-50 transition-colors cursor-pointer">
              <Upload className="w-4 h-4" />
              <input
                type="file"
                accept=".json"
                onChange={handleImportTasks}
                className="hidden"
              />
            </label>

            {/* Add Task */}
            <button
              onClick={() => {
                setEditingTask(null);
                setShowTaskForm(true);
              }}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Task
            </button>
          </div>
        </div>

        {/* Stats */}
        <TaskStats
          stats={stats}
          completionRate={completionRate}
          overdueCount={overdueCount}
          upcomingDeadlines={upcomingDeadlines}
        />
      </div>

      {/* Filters */}
      {showFilters && (
        <TaskFilters
          filters={filters}
          updateFilter={updateFilter}
          clearFilters={clearFilters}
          getFilterCounts={getFilterCounts}
          tasks={tasks}
        />
      )}

      {/* Content */}
      <div className="flex-1 overflow-hidden">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <LoadingSpinner />
          </div>
        ) : error ? (
          <div className="p-6">
            <ErrorMessage
              message={error}
              onRetry={refreshTasks}
              onDismiss={clearError}
            />
          </div>
        ) : (
          <TaskList
            tasks={filteredTasks}
            onEdit={handleEditTask}
            onDelete={handleDeleteTask}
            onToggleStatus={toggleTaskStatus}
          />
        )}
      </div>

      {/* Task Form Modal */}
      {showTaskForm && (
        <TaskForm
          task={editingTask}
          onSubmit={editingTask ? handleUpdateTask : handleCreateTask}
          onCancel={() => {
            setShowTaskForm(false);
            setEditingTask(null);
          }}
        />
      )}

      {/* Achievement Notifications */}
      {unlockedAchievements.map((achievement) => (
        <AchievementNotification
          key={achievement.id}
          achievement={achievement}
          onDismiss={() => markAchievementSeen(achievement.id)}
        />
      ))}
    </div>
  );
};

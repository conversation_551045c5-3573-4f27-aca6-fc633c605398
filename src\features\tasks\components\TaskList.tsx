import React, { useState, useMemo } from 'react';
import { Task } from '../../../types';
import { TaskCard } from './TaskCard';
import { EmptyState } from '../../../components/ui/EmptyState';
import { VirtualizedList } from '../../../components/ui/VirtualizedList';

interface TaskListProps {
  tasks: Task[];
  onEdit: (task: Task) => void;
  onDelete: (taskId: string) => void;
  onToggleStatus: (taskId: string) => void;
  onToggleSubtask?: (taskId: string, subtaskId: string) => void;
  groupBy?: 'none' | 'status' | 'priority' | 'subject' | 'dueDate';
  sortBy?: 'dueDate' | 'priority' | 'title' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
  enableVirtualization?: boolean;
  className?: string;
}

export const TaskList: React.FC<TaskListProps> = ({
  tasks,
  onEdit,
  onDelete,
  onToggleStatus,
  onToggleSubtask,
  groupBy = 'status',
  sortBy = 'dueDate',
  sortOrder = 'asc',
  enableVirtualization = false,
  className = '',
}) => {
  const [collapsedGroups, setCollapsedGroups] = useState<Set<string>>(new Set());

  const groupedTasks = useMemo(() => {
    if (groupBy === 'none') {
      return { 'All Tasks': tasks };
    }

    const groups: Record<string, Task[]> = {};

    tasks.forEach((task) => {
      let groupKey: string;

      switch (groupBy) {
        case 'status':
          groupKey = task.status === 'completed' ? 'Completed' : 'Pending';
          break;
        case 'priority':
          groupKey = task.priority.charAt(0).toUpperCase() + task.priority.slice(1);
          break;
        case 'subject':
          groupKey = task.subject || 'No Subject';
          break;
        case 'dueDate':
          const dueDate = new Date(task.dueDate);
          const today = new Date();
          const tomorrow = new Date(today);
          tomorrow.setDate(tomorrow.getDate() + 1);
          
          if (dueDate.toDateString() === today.toDateString()) {
            groupKey = 'Today';
          } else if (dueDate.toDateString() === tomorrow.toDateString()) {
            groupKey = 'Tomorrow';
          } else if (dueDate < today) {
            groupKey = 'Overdue';
          } else {
            groupKey = 'Upcoming';
          }
          break;
        default:
          groupKey = 'All Tasks';
      }

      if (!groups[groupKey]) {
        groups[groupKey] = [];
      }
      groups[groupKey].push(task);
    });

    // Sort groups
    const sortedGroups: Record<string, Task[]> = {};
    const groupOrder = getGroupOrder(groupBy);
    
    groupOrder.forEach((groupKey) => {
      if (groups[groupKey]) {
        sortedGroups[groupKey] = sortTasks(groups[groupKey], sortBy, sortOrder);
      }
    });

    // Add any remaining groups not in the predefined order
    Object.keys(groups).forEach((groupKey) => {
      if (!sortedGroups[groupKey]) {
        sortedGroups[groupKey] = sortTasks(groups[groupKey], sortBy, sortOrder);
      }
    });

    return sortedGroups;
  }, [tasks, groupBy, sortBy, sortOrder]);

  const toggleGroup = (groupKey: string) => {
    const newCollapsed = new Set(collapsedGroups);
    if (newCollapsed.has(groupKey)) {
      newCollapsed.delete(groupKey);
    } else {
      newCollapsed.add(groupKey);
    }
    setCollapsedGroups(newCollapsed);
  };

  const renderTaskCard = (task: Task, index: number) => (
    <TaskCard
      key={task.id}
      task={task}
      onEdit={onEdit}
      onDelete={onDelete}
      onToggleStatus={onToggleStatus}
      onToggleSubtask={onToggleSubtask}
      className="mb-3"
    />
  );

  if (tasks.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <EmptyState
          icon="📝"
          title="No tasks yet"
          description="Create your first task to get started with organizing your work."
        />
      </div>
    );
  }

  return (
    <div className={`p-6 overflow-auto ${className}`}>
      {Object.entries(groupedTasks).map(([groupKey, groupTasks]) => {
        if (groupTasks.length === 0) return null;

        const isCollapsed = collapsedGroups.has(groupKey);

        return (
          <div key={groupKey} className="mb-6">
            {/* Group Header */}
            {groupBy !== 'none' && (
              <div
                className="flex items-center justify-between mb-4 cursor-pointer"
                onClick={() => toggleGroup(groupKey)}
              >
                <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                  <span>{groupKey}</span>
                  <span className="text-sm font-normal text-gray-500">
                    ({groupTasks.length})
                  </span>
                </h3>
                <button className="text-gray-400 hover:text-gray-600">
                  {isCollapsed ? '▶' : '▼'}
                </button>
              </div>
            )}

            {/* Group Tasks */}
            {!isCollapsed && (
              <div className="space-y-3">
                {enableVirtualization && groupTasks.length > 50 ? (
                  <VirtualizedList
                    items={groupTasks}
                    renderItem={renderTaskCard}
                    itemHeight={120}
                    className="h-96"
                  />
                ) : (
                  groupTasks.map((task, index) => renderTaskCard(task, index))
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
};

// Helper functions
function getGroupOrder(groupBy: string): string[] {
  switch (groupBy) {
    case 'status':
      return ['Pending', 'Completed'];
    case 'priority':
      return ['Urgent', 'High', 'Medium', 'Low'];
    case 'dueDate':
      return ['Overdue', 'Today', 'Tomorrow', 'Upcoming'];
    default:
      return [];
  }
}

function sortTasks(tasks: Task[], sortBy: string, sortOrder: 'asc' | 'desc'): Task[] {
  const sorted = [...tasks].sort((a, b) => {
    let comparison = 0;

    switch (sortBy) {
      case 'dueDate':
        comparison = new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime();
        break;
      case 'priority':
        const priorityOrder = { urgent: 4, high: 3, medium: 2, low: 1 };
        comparison = (priorityOrder[b.priority as keyof typeof priorityOrder] || 2) - 
                    (priorityOrder[a.priority as keyof typeof priorityOrder] || 2);
        break;
      case 'title':
        comparison = a.title.localeCompare(b.title);
        break;
      case 'createdAt':
        comparison = new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime();
        break;
      default:
        comparison = 0;
    }

    return sortOrder === 'desc' ? -comparison : comparison;
  });

  return sorted;
}

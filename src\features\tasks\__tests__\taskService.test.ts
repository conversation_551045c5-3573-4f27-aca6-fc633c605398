import { taskService } from '../services/taskService';
import { firestoreUserTasks } from '../../../utils/firestoreUserTasks';
import { Task, TaskFormData } from '../../../types';

// Mock the firestore service
jest.mock('../../../utils/firestoreUserTasks');
const mockFirestoreUserTasks = firestoreUserTasks as jest.Mocked<typeof firestoreUserTasks>;

describe('TaskService', () => {
  const mockUserId = 'test-user-123';
  const mockTask: Task = {
    id: 'task-1',
    title: 'Test Task',
    description: 'Test Description',
    subject: 'Work',
    dueDate: '2024-01-01',
    priority: 'medium',
    status: 'pending',
    userId: mockUserId,
    createdAt: '2024-01-01T00:00:00.000Z',
    updatedAt: '2024-01-01T00:00:00.000Z',
  };

  const mockTaskFormData: TaskFormData = {
    title: 'New Task',
    description: 'New Description',
    subject: 'Personal',
    dueDate: '2024-01-02',
    priority: 'high',
    subtasks: [],
    recurrence: null,
    tags: ['urgent'],
    estimatedTime: 60,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    taskService.clearCache();
  });

  describe('getTasks', () => {
    it('should fetch tasks from firestore', async () => {
      mockFirestoreUserTasks.getTasks.mockResolvedValue([mockTask]);

      const result = await taskService.getTasks(mockUserId);

      expect(mockFirestoreUserTasks.getTasks).toHaveBeenCalledWith(mockUserId);
      expect(result).toEqual([mockTask]);
    });

    it('should use cache when available', async () => {
      mockFirestoreUserTasks.getTasks.mockResolvedValue([mockTask]);

      // First call
      await taskService.getTasks(mockUserId);
      // Second call should use cache
      const result = await taskService.getTasks(mockUserId, true);

      expect(mockFirestoreUserTasks.getTasks).toHaveBeenCalledTimes(1);
      expect(result).toEqual([mockTask]);
    });

    it('should handle errors and return cached data if available', async () => {
      // First, populate cache
      mockFirestoreUserTasks.getTasks.mockResolvedValueOnce([mockTask]);
      await taskService.getTasks(mockUserId);

      // Then simulate error
      mockFirestoreUserTasks.getTasks.mockRejectedValue(new Error('Network error'));

      const result = await taskService.getTasks(mockUserId, false);

      expect(result).toEqual([mockTask]);
    });
  });

  describe('createTask', () => {
    it('should create a new task', async () => {
      mockFirestoreUserTasks.addTask.mockResolvedValue(undefined);
      mockFirestoreUserTasks.getTasks.mockResolvedValue([]);

      await taskService.createTask(mockUserId, mockTaskFormData);

      expect(mockFirestoreUserTasks.addTask).toHaveBeenCalledWith(
        mockUserId,
        expect.objectContaining({
          title: mockTaskFormData.title,
          description: mockTaskFormData.description,
          subject: mockTaskFormData.subject,
          dueDate: mockTaskFormData.dueDate,
          priority: mockTaskFormData.priority,
          status: 'pending',
        })
      );
    });

    it('should handle creation errors', async () => {
      mockFirestoreUserTasks.addTask.mockRejectedValue(new Error('Creation failed'));

      await expect(taskService.createTask(mockUserId, mockTaskFormData))
        .rejects.toThrow('Failed to create task');
    });
  });

  describe('updateTask', () => {
    it('should update an existing task', async () => {
      mockFirestoreUserTasks.updateTask.mockResolvedValue(undefined);
      mockFirestoreUserTasks.getTasks.mockResolvedValue([]);

      const updates = { title: 'Updated Title' };
      await taskService.updateTask(mockUserId, mockTask.id, updates);

      expect(mockFirestoreUserTasks.updateTask).toHaveBeenCalledWith(
        mockUserId,
        mockTask.id,
        expect.objectContaining({
          ...updates,
          updatedAt: expect.any(String),
        })
      );
    });

    it('should handle recurring tasks when completed', async () => {
      const recurringTask: Task = {
        ...mockTask,
        recurrence: { type: 'daily', interval: 1 },
      };

      mockFirestoreUserTasks.updateTask.mockResolvedValue(undefined);
      mockFirestoreUserTasks.getTasks.mockResolvedValue([recurringTask]);
      mockFirestoreUserTasks.addTask.mockResolvedValue(undefined);

      await taskService.updateTask(mockUserId, recurringTask.id, { status: 'completed' });

      expect(mockFirestoreUserTasks.addTask).toHaveBeenCalled();
    });
  });

  describe('deleteTask', () => {
    it('should delete a task', async () => {
      mockFirestoreUserTasks.deleteTask.mockResolvedValue(undefined);

      await taskService.deleteTask(mockUserId, mockTask.id);

      expect(mockFirestoreUserTasks.deleteTask).toHaveBeenCalledWith(
        mockUserId,
        mockTask.id
      );
    });

    it('should handle deletion errors', async () => {
      mockFirestoreUserTasks.deleteTask.mockRejectedValue(new Error('Deletion failed'));

      await expect(taskService.deleteTask(mockUserId, mockTask.id))
        .rejects.toThrow('Failed to delete task');
    });
  });

  describe('getTaskStats', () => {
    it('should calculate task statistics correctly', () => {
      const tasks: Task[] = [
        { ...mockTask, status: 'pending', dueDate: '2024-01-01' },
        { ...mockTask, id: 'task-2', status: 'completed', dueDate: '2024-01-01' },
        { ...mockTask, id: 'task-3', status: 'pending', dueDate: '2023-12-31' }, // overdue
      ];

      const stats = taskService.getTaskStats(tasks);

      expect(stats.total).toBe(3);
      expect(stats.pending).toBe(2);
      expect(stats.completed).toBe(1);
      expect(stats.overdue).toBe(1);
    });
  });

  describe('searchTasks', () => {
    it('should filter tasks by search query', () => {
      const tasks: Task[] = [
        { ...mockTask, title: 'Important meeting' },
        { ...mockTask, id: 'task-2', title: 'Buy groceries', description: 'milk and bread' },
        { ...mockTask, id: 'task-3', title: 'Exercise', tags: ['health'] },
      ];

      const result = taskService.searchTasks(tasks, 'meeting');
      expect(result).toHaveLength(1);
      expect(result[0].title).toBe('Important meeting');

      const result2 = taskService.searchTasks(tasks, 'milk');
      expect(result2).toHaveLength(1);
      expect(result2[0].title).toBe('Buy groceries');

      const result3 = taskService.searchTasks(tasks, 'health');
      expect(result3).toHaveLength(1);
      expect(result3[0].title).toBe('Exercise');
    });

    it('should return all tasks for empty query', () => {
      const tasks: Task[] = [mockTask];
      const result = taskService.searchTasks(tasks, '');
      expect(result).toEqual(tasks);
    });
  });

  describe('exportTasks', () => {
    it('should export tasks as JSON string', () => {
      const tasks: Task[] = [mockTask];
      const result = taskService.exportTasks(tasks);
      
      expect(typeof result).toBe('string');
      expect(JSON.parse(result)).toEqual(tasks);
    });
  });

  describe('validateImportData', () => {
    it('should validate correct import data', () => {
      const validData = JSON.stringify([mockTask]);
      const result = taskService.validateImportData(validData);
      
      expect(result).toEqual([mockTask]);
    });

    it('should throw error for invalid JSON', () => {
      expect(() => taskService.validateImportData('invalid json'))
        .toThrow('Invalid import data');
    });

    it('should throw error for non-array data', () => {
      expect(() => taskService.validateImportData('{}'))
        .toThrow('Import data must be an array');
    });

    it('should throw error for tasks missing required fields', () => {
      const invalidTask = { id: '1' }; // missing title and dueDate
      expect(() => taskService.validateImportData(JSON.stringify([invalidTask])))
        .toThrow('Invalid task at index 0: missing required fields');
    });
  });
});

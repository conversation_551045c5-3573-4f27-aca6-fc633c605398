import { useState, useCallback } from 'react';
import { Task } from '../../../types';

export interface DragAndDropState {
  draggedTask: Task | null;
  dragOverTask: Task | null;
  isDragging: boolean;
}

export interface UseDragAndDropReturn {
  dragState: DragAndDropState;
  handleDragStart: (task: Task) => void;
  handleDragEnd: () => void;
  handleDragOver: (task: Task) => void;
  handleDragLeave: () => void;
  handleDrop: (targetTask: Task) => void;
  getDragProps: (task: Task) => {
    draggable: boolean;
    onDragStart: (e: React.DragEvent) => void;
    onDragEnd: () => void;
    onDragOver: (e: React.DragEvent) => void;
    onDragLeave: () => void;
    onDrop: (e: React.DragEvent) => void;
  };
}

export function useDragAndDrop(
  onReorder: (draggedTask: Task, targetTask: Task, position: 'before' | 'after') => void
): UseDragAndDropReturn {
  const [dragState, setDragState] = useState<DragAndDropState>({
    draggedTask: null,
    dragOverTask: null,
    isDragging: false,
  });

  const handleDragStart = useCallback((task: Task) => {
    setDragState({
      draggedTask: task,
      dragOverTask: null,
      isDragging: true,
    });
  }, []);

  const handleDragEnd = useCallback(() => {
    setDragState({
      draggedTask: null,
      dragOverTask: null,
      isDragging: false,
    });
  }, []);

  const handleDragOver = useCallback((task: Task) => {
    setDragState(prev => ({
      ...prev,
      dragOverTask: task,
    }));
  }, []);

  const handleDragLeave = useCallback(() => {
    setDragState(prev => ({
      ...prev,
      dragOverTask: null,
    }));
  }, []);

  const handleDrop = useCallback((targetTask: Task) => {
    if (dragState.draggedTask && dragState.draggedTask.id !== targetTask.id) {
      // Determine drop position based on mouse position
      // For simplicity, we'll default to 'after'
      onReorder(dragState.draggedTask, targetTask, 'after');
    }
    handleDragEnd();
  }, [dragState.draggedTask, onReorder, handleDragEnd]);

  const getDragProps = useCallback((task: Task) => ({
    draggable: true,
    onDragStart: (e: React.DragEvent) => {
      e.dataTransfer.effectAllowed = 'move';
      e.dataTransfer.setData('text/plain', task.id);
      handleDragStart(task);
    },
    onDragEnd: handleDragEnd,
    onDragOver: (e: React.DragEvent) => {
      e.preventDefault();
      e.dataTransfer.dropEffect = 'move';
      handleDragOver(task);
    },
    onDragLeave: handleDragLeave,
    onDrop: (e: React.DragEvent) => {
      e.preventDefault();
      handleDrop(task);
    },
  }), [task, handleDragStart, handleDragEnd, handleDragOver, handleDragLeave, handleDrop]);

  return {
    dragState,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    getDragProps,
  };
}

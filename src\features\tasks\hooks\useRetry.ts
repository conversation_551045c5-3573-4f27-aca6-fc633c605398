import { useState, useCallback } from 'react';

export interface RetryConfig {
  maxAttempts?: number;
  delay?: number;
  backoffMultiplier?: number;
  maxDelay?: number;
}

export interface UseRetryReturn {
  isRetrying: boolean;
  retryCount: number;
  retry: <T>(fn: () => Promise<T>, config?: RetryConfig) => Promise<T>;
  reset: () => void;
}

const DEFAULT_CONFIG: Required<RetryConfig> = {
  maxAttempts: 3,
  delay: 1000,
  backoffMultiplier: 2,
  maxDelay: 10000,
};

export function useRetry(): UseRetryReturn {
  const [isRetrying, setIsRetrying] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

  const retry = useCallback(async <T>(
    fn: () => Promise<T>,
    config: RetryConfig = {}
  ): Promise<T> => {
    const finalConfig = { ...DEFAULT_CONFIG, ...config };
    let lastError: Error;
    
    setIsRetrying(true);
    setRetryCount(0);

    for (let attempt = 1; attempt <= finalConfig.maxAttempts; attempt++) {
      try {
        setRetryCount(attempt);
        const result = await fn();
        setIsRetrying(false);
        setRetryCount(0);
        return result;
      } catch (error) {
        lastError = error as Error;
        
        if (attempt === finalConfig.maxAttempts) {
          setIsRetrying(false);
          throw lastError;
        }

        // Calculate delay with exponential backoff
        const delay = Math.min(
          finalConfig.delay * Math.pow(finalConfig.backoffMultiplier, attempt - 1),
          finalConfig.maxDelay
        );

        console.warn(`Attempt ${attempt} failed, retrying in ${delay}ms...`, error);
        await sleep(delay);
      }
    }

    setIsRetrying(false);
    throw lastError!;
  }, []);

  const reset = useCallback(() => {
    setIsRetrying(false);
    setRetryCount(0);
  }, []);

  return {
    isRetrying,
    retryCount,
    retry,
    reset,
  };
}

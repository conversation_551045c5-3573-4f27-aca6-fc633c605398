import { Task, TaskFormData, TaskStats } from "../../../types";
import { firestoreUserTasks } from "../../../utils/firestoreUserTasks";
import { generateRecurringTask } from "../utils/taskUtils";

export interface TaskServiceConfig {
  enableOfflineSupport?: boolean;
  enableOptimisticUpdates?: boolean;
  cacheTimeout?: number;
}

class TaskService {
  private cache = new Map<string, { data: Task[]; timestamp: number }>();
  private config: TaskServiceConfig;
  private readonly CACHE_TIMEOUT = 5 * 60 * 1000; // 5 minutes

  constructor(config: TaskServiceConfig = {}) {
    this.config = {
      enableOfflineSupport: true,
      enableOptimisticUpdates: true,
      cacheTimeout: this.CACHE_TIMEOUT,
      ...config,
    };
  }

  async getTasks(userId: string, useCache = true): Promise<Task[]> {
    try {
      // Check cache first
      if (useCache && this.isCacheValid(userId)) {
        return this.cache.get(userId)!.data;
      }

      const tasks = await firestoreUserTasks.getTasks(userId);

      // Update cache
      this.updateCache(userId, tasks);

      return tasks;
    } catch (error) {
      console.error("Error fetching tasks:", error);

      // Return cached data if available during error
      if (this.cache.has(userId)) {
        console.warn("Using cached data due to fetch error");
        return this.cache.get(userId)!.data;
      }

      throw new Error("Failed to fetch tasks");
    }
  }

  async createTask(userId: string, taskData: TaskFormData): Promise<Task> {
    try {
      const newTask: Omit<Task, "id" | "userId"> = {
        ...taskData,
        status: "pending",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      // Optimistic update
      if (this.config.enableOptimisticUpdates) {
        const optimisticTask: Task = {
          ...newTask,
          id: `temp-${Date.now()}`,
          userId,
        };
        this.addToCache(userId, optimisticTask);
      }

      await firestoreUserTasks.addTask(userId, newTask);

      // Refresh cache
      await this.getTasks(userId, false);

      return newTask as Task;
    } catch (error) {
      console.error("Error creating task:", error);

      // Remove optimistic update on error
      if (this.config.enableOptimisticUpdates) {
        await this.getTasks(userId, false);
      }

      throw new Error("Failed to create task");
    }
  }

  async updateTask(
    userId: string,
    taskId: string,
    updates: Partial<Task>
  ): Promise<void> {
    try {
      const updatedData = {
        ...updates,
        updatedAt: new Date().toISOString(),
      };

      // Optimistic update
      if (this.config.enableOptimisticUpdates) {
        this.updateInCache(userId, taskId, updatedData);
      }

      await firestoreUserTasks.updateTask(userId, taskId, updatedData);

      // Handle recurring tasks
      if (updates.status === "completed") {
        const tasks = await this.getTasks(userId);
        const task = tasks.find((t) => t.id === taskId);

        if (task?.recurrence) {
          const nextTask = generateRecurringTask(task);
          await this.createTask(userId, nextTask as TaskFormData);
        }
      }
    } catch (error) {
      console.error("Error updating task:", error);

      // Revert optimistic update on error
      if (this.config.enableOptimisticUpdates) {
        await this.getTasks(userId, false);
      }

      throw new Error("Failed to update task");
    }
  }

  async deleteTask(userId: string, taskId: string): Promise<void> {
    try {
      // Optimistic update
      if (this.config.enableOptimisticUpdates) {
        this.removeFromCache(userId, taskId);
      }

      await firestoreUserTasks.deleteTask(userId, taskId);
    } catch (error) {
      console.error("Error deleting task:", error);

      // Revert optimistic update on error
      if (this.config.enableOptimisticUpdates) {
        await this.getTasks(userId, false);
      }

      throw new Error("Failed to delete task");
    }
  }

  async bulkUpdateTasks(
    userId: string,
    updates: Array<{ id: string; updates: Partial<Task> }>
  ): Promise<void> {
    try {
      const promises = updates.map(({ id, updates: taskUpdates }) =>
        this.updateTask(userId, id, taskUpdates)
      );

      await Promise.all(promises);
    } catch (error) {
      console.error("Error in bulk update:", error);
      throw new Error("Failed to update multiple tasks");
    }
  }

  getTaskStats(tasks: Task[]): TaskStats {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    const weekEnd = new Date(today);
    weekEnd.setDate(weekEnd.getDate() + 7);

    return {
      total: tasks.length,
      pending: tasks.filter((t) => t.status === "pending").length,
      completed: tasks.filter((t) => t.status === "completed").length,
      overdue: tasks.filter(
        (t) => t.status === "pending" && new Date(t.dueDate) < today
      ).length,
      today: tasks.filter(
        (t) => new Date(t.dueDate).toDateString() === today.toDateString()
      ).length,
      tomorrow: tasks.filter(
        (t) => new Date(t.dueDate).toDateString() === tomorrow.toDateString()
      ).length,
      thisWeek: tasks.filter(
        (t) => new Date(t.dueDate) >= today && new Date(t.dueDate) <= weekEnd
      ).length,
    };
  }

  // Cache management methods
  private isCacheValid(userId: string): boolean {
    const cached = this.cache.get(userId);
    if (!cached) return false;

    return (
      Date.now() - cached.timestamp <
      (this.config.cacheTimeout || this.CACHE_TIMEOUT)
    );
  }

  private updateCache(userId: string, tasks: Task[]): void {
    this.cache.set(userId, {
      data: tasks,
      timestamp: Date.now(),
    });
  }

  private addToCache(userId: string, task: Task): void {
    const cached = this.cache.get(userId);
    if (cached) {
      cached.data.push(task);
    }
  }

  private updateInCache(
    userId: string,
    taskId: string,
    updates: Partial<Task>
  ): void {
    const cached = this.cache.get(userId);
    if (cached) {
      const taskIndex = cached.data.findIndex((t) => t.id === taskId);
      if (taskIndex !== -1) {
        cached.data[taskIndex] = { ...cached.data[taskIndex], ...updates };
      }
    }
  }

  private removeFromCache(userId: string, taskId: string): void {
    const cached = this.cache.get(userId);
    if (cached) {
      cached.data = cached.data.filter((t) => t.id !== taskId);
    }
  }

  clearCache(userId?: string): void {
    if (userId) {
      this.cache.delete(userId);
    } else {
      this.cache.clear();
    }
  }

  // Offline support methods
  async syncOfflineChanges(userId: string): Promise<void> {
    if (!this.config.enableOfflineSupport) return;

    try {
      const offlineChanges = this.getOfflineChanges(userId);

      for (const change of offlineChanges) {
        switch (change.type) {
          case "create":
            await firestoreUserTasks.addTask(userId, change.data);
            break;
          case "update":
            await firestoreUserTasks.updateTask(userId, change.id, change.data);
            break;
          case "delete":
            await firestoreUserTasks.deleteTask(userId, change.id);
            break;
        }
      }

      this.clearOfflineChanges(userId);
      await this.getTasks(userId, false); // Refresh cache
    } catch (error) {
      console.error("Error syncing offline changes:", error);
      throw new Error("Failed to sync offline changes");
    }
  }

  private getOfflineChanges(userId: string): any[] {
    try {
      const changes = localStorage.getItem(`offline_changes_${userId}`);
      return changes ? JSON.parse(changes) : [];
    } catch {
      return [];
    }
  }

  private saveOfflineChange(userId: string, change: any): void {
    try {
      const changes = this.getOfflineChanges(userId);
      changes.push(change);
      localStorage.setItem(
        `offline_changes_${userId}`,
        JSON.stringify(changes)
      );
    } catch (error) {
      console.error("Error saving offline change:", error);
    }
  }

  private clearOfflineChanges(userId: string): void {
    try {
      localStorage.removeItem(`offline_changes_${userId}`);
    } catch (error) {
      console.error("Error clearing offline changes:", error);
    }
  }

  // Search and filtering
  searchTasks(tasks: Task[], query: string): Task[] {
    if (!query.trim()) return tasks;

    const searchLower = query.toLowerCase();
    return tasks.filter(
      (task) =>
        task.title.toLowerCase().includes(searchLower) ||
        task.description.toLowerCase().includes(searchLower) ||
        task.subject.toLowerCase().includes(searchLower) ||
        (task.tags &&
          task.tags.some((tag) => tag.toLowerCase().includes(searchLower)))
    );
  }

  // Export/Import functionality
  exportTasks(tasks: Task[]): string {
    try {
      return JSON.stringify(tasks, null, 2);
    } catch (error) {
      throw new Error("Failed to export tasks");
    }
  }

  validateImportData(data: string): Task[] {
    try {
      const parsed = JSON.parse(data);
      if (!Array.isArray(parsed)) {
        throw new Error("Import data must be an array");
      }

      // Validate each task
      parsed.forEach((task, index) => {
        if (!task.title || !task.dueDate) {
          throw new Error(
            `Invalid task at index ${index}: missing required fields`
          );
        }
      });

      return parsed;
    } catch (error) {
      throw new Error(
        `Invalid import data: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    }
  }

  async importTasks(userId: string, data: string): Promise<void> {
    const tasks = this.validateImportData(data);

    try {
      for (const taskData of tasks) {
        const { id, userId: _, ...taskWithoutId } = taskData;
        await firestoreUserTasks.addTask(userId, taskWithoutId);
      }

      await this.getTasks(userId, false); // Refresh cache
    } catch (error) {
      throw new Error("Failed to import tasks");
    }
  }
}

export const taskService = new TaskService();

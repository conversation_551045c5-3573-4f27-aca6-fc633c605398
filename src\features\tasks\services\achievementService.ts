import { Task, Achievement } from '../../../types';

class AchievementService {
  private achievements: Achievement[] = [
    {
      id: 'first_task',
      name: 'Getting Started',
      description: 'Create your first task',
      icon: '🎯',
      condition: 'create_first_task',
      unlocked: false,
      progress: 0,
      target: 1,
    },
    {
      id: 'task_completionist',
      name: 'Task Completionist',
      description: 'Complete 10 tasks',
      icon: '✅',
      condition: 'complete_10_tasks',
      unlocked: false,
      progress: 0,
      target: 10,
    },
    {
      id: 'productivity_streak',
      name: 'Productivity Streak',
      description: 'Complete tasks for 7 consecutive days',
      icon: '🔥',
      condition: 'complete_7_day_streak',
      unlocked: false,
      progress: 0,
      target: 7,
    },
    {
      id: 'early_bird',
      name: 'Early Bird',
      description: 'Complete 5 tasks before their due date',
      icon: '🌅',
      condition: 'complete_5_early',
      unlocked: false,
      progress: 0,
      target: 5,
    },
    {
      id: 'organization_master',
      name: 'Organization Master',
      description: 'Use subtasks in 10 different tasks',
      icon: '📋',
      condition: 'use_subtasks_10_times',
      unlocked: false,
      progress: 0,
      target: 10,
    },
    {
      id: 'priority_manager',
      name: 'Priority Manager',
      description: 'Complete 5 high priority tasks',
      icon: '⚡',
      condition: 'complete_5_high_priority',
      unlocked: false,
      progress: 0,
      target: 5,
    },
    {
      id: 'task_master',
      name: 'Task Master',
      description: 'Complete 50 tasks total',
      icon: '👑',
      condition: 'complete_50_tasks',
      unlocked: false,
      progress: 0,
      target: 50,
    },
    {
      id: 'recurring_champion',
      name: 'Recurring Champion',
      description: 'Create 5 recurring tasks',
      icon: '🔄',
      condition: 'create_5_recurring',
      unlocked: false,
      progress: 0,
      target: 5,
    },
    {
      id: 'subject_specialist',
      name: 'Subject Specialist',
      description: 'Complete tasks in 5 different subjects',
      icon: '📚',
      condition: 'complete_5_subjects',
      unlocked: false,
      progress: 0,
      target: 5,
    },
    {
      id: 'time_estimator',
      name: 'Time Estimator',
      description: 'Set estimated time for 10 tasks',
      icon: '⏱️',
      condition: 'estimate_time_10_tasks',
      unlocked: false,
      progress: 0,
      target: 10,
    },
  ];

  getAchievements(): Achievement[] {
    // Load from localStorage if available
    const stored = localStorage.getItem('task_achievements');
    if (stored) {
      try {
        this.achievements = JSON.parse(stored);
      } catch (error) {
        console.error('Error loading achievements from localStorage:', error);
      }
    }
    return [...this.achievements];
  }

  private saveAchievements(): void {
    try {
      localStorage.setItem('task_achievements', JSON.stringify(this.achievements));
    } catch (error) {
      console.error('Error saving achievements to localStorage:', error);
    }
  }

  async checkAchievements(tasks: Task[]): Promise<Achievement[]> {
    const newlyUnlocked: Achievement[] = [];
    const completedTasks = tasks.filter(task => task.status === 'completed');
    const now = new Date();

    for (const achievement of this.achievements) {
      if (achievement.unlocked) continue;

      let progress = 0;
      let shouldUnlock = false;

      switch (achievement.condition) {
        case 'create_first_task':
          progress = tasks.length > 0 ? 1 : 0;
          shouldUnlock = progress >= 1;
          break;

        case 'complete_10_tasks':
          progress = completedTasks.length;
          shouldUnlock = progress >= 10;
          break;

        case 'complete_50_tasks':
          progress = completedTasks.length;
          shouldUnlock = progress >= 50;
          break;

        case 'complete_7_day_streak':
          progress = this.calculateCompletionStreak(completedTasks);
          shouldUnlock = progress >= 7;
          break;

        case 'complete_5_early':
          progress = completedTasks.filter(task => {
            const completedDate = new Date(task.updatedAt || task.createdAt);
            const dueDate = new Date(task.dueDate);
            return completedDate < dueDate;
          }).length;
          shouldUnlock = progress >= 5;
          break;

        case 'use_subtasks_10_times':
          progress = tasks.filter(task => task.subtasks && task.subtasks.length > 0).length;
          shouldUnlock = progress >= 10;
          break;

        case 'complete_5_high_priority':
          progress = completedTasks.filter(task => task.priority === 'high').length;
          shouldUnlock = progress >= 5;
          break;

        case 'create_5_recurring':
          progress = tasks.filter(task => task.recurrence).length;
          shouldUnlock = progress >= 5;
          break;

        case 'complete_5_subjects':
          const subjects = new Set(completedTasks.map(task => task.subject).filter(s => s.trim()));
          progress = subjects.size;
          shouldUnlock = progress >= 5;
          break;

        case 'estimate_time_10_tasks':
          progress = tasks.filter(task => task.estimatedTime && task.estimatedTime > 0).length;
          shouldUnlock = progress >= 10;
          break;
      }

      // Update progress
      achievement.progress = progress;

      // Unlock if conditions are met
      if (shouldUnlock && !achievement.unlocked) {
        achievement.unlocked = true;
        achievement.unlockedAt = now.toISOString();
        newlyUnlocked.push({ ...achievement });
      }
    }

    if (newlyUnlocked.length > 0) {
      this.saveAchievements();
    }

    return newlyUnlocked;
  }

  private calculateCompletionStreak(completedTasks: Task[]): number {
    if (completedTasks.length === 0) return 0;

    // Sort tasks by completion date
    const sortedTasks = completedTasks
      .map(task => new Date(task.updatedAt || task.createdAt))
      .sort((a, b) => b.getTime() - a.getTime());

    let streak = 0;
    let currentDate = new Date();
    currentDate.setHours(0, 0, 0, 0);

    // Check each day going backwards
    for (let i = 0; i < 30; i++) { // Check up to 30 days
      const checkDate = new Date(currentDate);
      checkDate.setDate(checkDate.getDate() - i);

      const hasTaskOnDate = sortedTasks.some(taskDate => {
        const taskDay = new Date(taskDate);
        taskDay.setHours(0, 0, 0, 0);
        return taskDay.getTime() === checkDate.getTime();
      });

      if (hasTaskOnDate) {
        streak++;
      } else if (i > 0) { // Don't break on the first day (today) if no tasks
        break;
      }
    }

    return streak;
  }

  resetAchievements(): void {
    this.achievements = this.achievements.map(achievement => ({
      ...achievement,
      unlocked: false,
      progress: 0,
      unlockedAt: undefined,
    }));
    this.saveAchievements();
  }

  getUnlockedAchievements(): Achievement[] {
    return this.achievements.filter(achievement => achievement.unlocked);
  }

  getAchievementProgress(achievementId: string): number {
    const achievement = this.achievements.find(a => a.id === achievementId);
    if (!achievement || !achievement.target) return 0;
    
    return Math.min(100, Math.round(((achievement.progress || 0) / achievement.target) * 100));
  }
}

export const achievementService = new AchievementService();

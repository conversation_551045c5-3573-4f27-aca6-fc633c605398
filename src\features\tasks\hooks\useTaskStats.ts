import { useMemo } from 'react';
import { Task, TaskStats } from '../../../types';
import { taskService } from '../services/taskService';

export interface UseTaskStatsReturn {
  stats: TaskStats;
  completionRate: number;
  averageCompletionTime: number;
  productivityTrend: 'up' | 'down' | 'stable';
  upcomingDeadlines: Task[];
  overdueCount: number;
}

export function useTaskStats(tasks: Task[]): UseTaskStatsReturn {
  const stats = useMemo(() => {
    return taskService.getTaskStats(tasks);
  }, [tasks]);

  const completionRate = useMemo(() => {
    if (stats.total === 0) return 0;
    return Math.round((stats.completed / stats.total) * 100);
  }, [stats]);

  const averageCompletionTime = useMemo(() => {
    const completedTasks = tasks.filter(task => 
      task.status === 'completed' && 
      task.actualTime && 
      task.actualTime > 0
    );

    if (completedTasks.length === 0) return 0;

    const totalTime = completedTasks.reduce((sum, task) => sum + (task.actualTime || 0), 0);
    return Math.round(totalTime / completedTasks.length);
  }, [tasks]);

  const productivityTrend = useMemo((): 'up' | 'down' | 'stable' => {
    const now = new Date();
    const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    const twoWeeksAgo = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);

    const thisWeekCompleted = tasks.filter(task => 
      task.status === 'completed' && 
      new Date(task.updatedAt || task.createdAt) >= lastWeek
    ).length;

    const lastWeekCompleted = tasks.filter(task => 
      task.status === 'completed' && 
      new Date(task.updatedAt || task.createdAt) >= twoWeeksAgo &&
      new Date(task.updatedAt || task.createdAt) < lastWeek
    ).length;

    if (thisWeekCompleted > lastWeekCompleted) return 'up';
    if (thisWeekCompleted < lastWeekCompleted) return 'down';
    return 'stable';
  }, [tasks]);

  const upcomingDeadlines = useMemo(() => {
    const now = new Date();
    const nextWeek = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);

    return tasks
      .filter(task => 
        task.status === 'pending' &&
        new Date(task.dueDate) >= now &&
        new Date(task.dueDate) <= nextWeek
      )
      .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
      .slice(0, 5); // Top 5 upcoming deadlines
  }, [tasks]);

  const overdueCount = useMemo(() => {
    const now = new Date();
    return tasks.filter(task => 
      task.status === 'pending' && 
      new Date(task.dueDate) < now
    ).length;
  }, [tasks]);

  return {
    stats,
    completionRate,
    averageCompletionTime,
    productivityTrend,
    upcomingDeadlines,
    overdueCount,
  };
}

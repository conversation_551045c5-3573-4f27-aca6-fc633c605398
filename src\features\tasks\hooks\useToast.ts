import { useState, useCallback } from 'react';
import { ToastProps, ToastType } from '../../../components/ui/Toast';

export interface UseToastReturn {
  toasts: ToastProps[];
  showToast: (type: ToastType, title: string, message?: string, duration?: number) => string;
  dismissToast: (id: string) => void;
  clearAllToasts: () => void;
  success: (title: string, message?: string) => string;
  error: (title: string, message?: string) => string;
  warning: (title: string, message?: string) => string;
  info: (title: string, message?: string) => string;
}

export function useToast(): UseToastReturn {
  const [toasts, setToasts] = useState<ToastProps[]>([]);

  const generateId = () => `toast_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  const showToast = useCallback((
    type: ToastType,
    title: string,
    message?: string,
    duration = 5000
  ): string => {
    const id = generateId();
    
    const newToast: ToastProps = {
      id,
      type,
      title,
      message,
      duration,
      onDismiss: (toastId) => dismissToast(toastId),
    };

    setToasts(prev => [...prev, newToast]);
    return id;
  }, []);

  const dismissToast = useCallback((id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);

  const clearAllToasts = useCallback(() => {
    setToasts([]);
  }, []);

  const success = useCallback((title: string, message?: string) => {
    return showToast('success', title, message);
  }, [showToast]);

  const error = useCallback((title: string, message?: string) => {
    return showToast('error', title, message, 7000); // Longer duration for errors
  }, [showToast]);

  const warning = useCallback((title: string, message?: string) => {
    return showToast('warning', title, message);
  }, [showToast]);

  const info = useCallback((title: string, message?: string) => {
    return showToast('info', title, message);
  }, [showToast]);

  return {
    toasts,
    showToast,
    dismissToast,
    clearAllToasts,
    success,
    error,
    warning,
    info,
  };
}

import React from "react";
import { Task } from "../types";

interface TaskListProps {
  tasks: Task[];
  onEdit: (task: Task) => void;
  onDelete: (taskId: string) => void;
  onToggle: (taskId: string) => void;
}

const TaskList: React.FC<TaskListProps> = ({
  tasks,
  onEdit,
  onDelete,
  onToggle,
}) => {
  return (
    <div>
      {/* Map tasks to TaskItem components here */}
      {/* ...existing code... */}
    </div>
  );
};

export default TaskList;

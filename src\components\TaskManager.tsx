import React, { useState, useEffect, useRef } from "react";
import TaskList from "./TaskList";
import TaskItem from "./TaskItem";
import TaskFormModal from "./TaskFormModal";
import AchievementsPanel from "./AchievementsPanel";
import TaskFilters from "./TaskFilters";
import { realTimeAuth } from "../utils/realTimeAuth";
import { Achievement, SubTask, RecurrencePattern, Task } from "../types";
import { firestoreUserTasks } from "../utils/firestoreUserTasks";
import { addDays, addWeeks, addMonths, isAfter, startOfDay } from "date-fns";

function TaskManager() {
  // Missing state variables
  const [priorityFilter, setPriorityFilter] = useState<string>("all");
  const [subjectFilter, setSubjectFilter] = useState<string>("all");
  const [dateRangeFilter, setDateRangeFilter] = useState<{ startDate: string; endDate: string }>({ startDate: "", endDate: "" });
  const [animatingTasks, setAnimatingTasks] = useState<Set<string>>(new Set());
  const [completedInSession, setCompletedInSession] = useState<number>(0);
  const [usedMessages, setUsedMessages] = useState<Set<string>>(new Set());
  // State variables and functions needed for the component
  const [tasks, setTasks] = useState<Task[]>([]);
  const [showAddTask, setShowAddTask] = useState(false);
  const [filter, setFilter] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [celebrationMessage, setCelebrationMessage] = useState<string>("");
  const [showCelebration, setShowCelebration] = useState(false);
  const [newSubtask, setNewSubtask] = useState<{ [taskId: string]: string }>({});
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);
  const [editingTask, setEditingTask] = useState<Task | null>(null);
  const [taskForm, setTaskForm] = useState({
    title: "",
    description: "",
    subject: "",
    dueDate: "",
    priority: "medium" as "low" | "medium" | "high",
    subtasks: [] as SubTask[],
    recurrence: null as RecurrencePattern | null,
  });

  const user = realTimeAuth.getCurrentUser();

  // Initialize achievements
  const initializeAchievements = (): Achievement[] => [
    {
      id: 'first_task',
      name: 'Getting Started',
      description: 'Create your first task',
      icon: '🎯',
      condition: 'create_first_task',
      unlocked: false,
      progress: 0,
      target: 1,
    },
    {
      id: 'early_bird',
      name: 'Early Bird',
      description: 'Complete a task before 8 AM',
      icon: '🌅',
      condition: 'complete_before_8am',
      unlocked: false,
      progress: 0,
      target: 1,
    },
    {
      id: 'streak_master',
      name: 'Streak Master',
      description: 'Complete tasks 7 days in a row',
      icon: '🔥',
      condition: '7_day_streak',
      unlocked: false,
      progress: 0,
      target: 7,
    },
    {
      id: 'task_master',
      name: 'Task Master',
      description: 'Complete 50 tasks total',
      icon: '👑',
      condition: 'complete_50_tasks',
      unlocked: false,
      progress: 0,
      target: 50,
    },
    {
      id: 'organization_guru',
      name: 'Organization Guru',
      description: 'Use subtasks in 5 different tasks',
      icon: '📋',
      condition: 'use_subtasks_5_times',
      unlocked: false,
      progress: 0,
      target: 5,
    }
  ];

  if (!user) {
    return (
      <div className="flex flex-col items-center justify-center h-full">
        <h2 className="text-2xl font-bold mb-4">
          Please log in to access your tasks.
        </h2>
        <p className="text-gray-600 mb-6">
          Sign in to create, view, and manage your personal cloud tasks.
        </p>
      </div>
    );
  }

  useEffect(() => {
    if (user) {
      loadTasks();
      // Initialize achievements if not exists
      const savedAchievements = localStorage.getItem(`achievements_${user.id}`);
      if (savedAchievements) {
        setAchievements(JSON.parse(savedAchievements));
      } else {
        const initialAchievements = initializeAchievements();
        setAchievements(initialAchievements);
        localStorage.setItem(`achievements_${user.id}`, JSON.stringify(initialAchievements));
      }
    }
  }, [user]);

  // Achievement system
  const checkAchievements = async (action: string) => {
    if (!user) return;

    const updatedAchievements = [...achievements];
    let hasNewAchievement = false;

    for (const achievement of updatedAchievements) {
      if (achievement.unlocked) continue;

      switch (achievement.condition) {
        case 'create_first_task':
          if (action === 'task_created') {
            achievement.progress = 1;
            achievement.unlocked = true;
            achievement.unlockedAt = new Date().toISOString();
            hasNewAchievement = true;
          }
          break;

        case 'complete_before_8am':
          if (action === 'task_completed' && new Date().getHours() < 8) {
            achievement.progress = 1;
            achievement.unlocked = true;
            achievement.unlockedAt = new Date().toISOString();
            hasNewAchievement = true;
          }
          break;

        case 'complete_50_tasks':
          if (action === 'task_completed') {
            const completedCount = tasks.filter(t => t.status === 'completed').length;
            achievement.progress = completedCount;
            if (completedCount >= 50) {
              achievement.unlocked = true;
              achievement.unlockedAt = new Date().toISOString();
              hasNewAchievement = true;
            }
          }
          break;

        case 'use_subtasks_5_times':
          if (action === 'subtask_used') {
            const tasksWithSubtasks = tasks.filter(t => t.subtasks && t.subtasks.length > 0).length;
            achievement.progress = tasksWithSubtasks;
            if (tasksWithSubtasks >= 5) {
              achievement.unlocked = true;
              achievement.unlockedAt = new Date().toISOString();
              hasNewAchievement = true;
            }
          }
          break;
      }
    }

    if (hasNewAchievement) {
      setAchievements(updatedAchievements);
      localStorage.setItem(`achievements_${user.id}`, JSON.stringify(updatedAchievements));
      
      // Show achievement notification
      const newAchievement = updatedAchievements.find(a => a.unlocked && !achievements.find(old => old.id === a.id && old.unlocked));
      if (newAchievement) {
        setCelebrationMessage(`🏆 Achievement Unlocked: ${newAchievement.name}!`);
        setShowCelebration(true);
        setTimeout(() => setShowCelebration(false), 3000);
      }
    }
  };

  // Subtask functions
  const addSubtask = (taskId: string) => {
    const subtaskTitle = newSubtask[taskId];
    if (!subtaskTitle?.trim()) return;

    const updatedTasks = tasks.map(task => {
      if (task.id === taskId) {
        const newSubtaskItem: SubTask = {
          id: Date.now().toString(),
          title: subtaskTitle.trim(),
          completed: false,
          createdAt: new Date().toISOString(),
        };
        return {
          ...task,
          subtasks: [...(task.subtasks || []), newSubtaskItem],
        };
      }
      return task;
    });

    setTasks(updatedTasks);
    setNewSubtask({ ...newSubtask, [taskId]: '' });
    checkAchievements('subtask_used');

    // Update in database
    const task = updatedTasks.find(t => t.id === taskId);
    if (task) {
      firestoreUserTasks.updateTask(user.id, taskId, { subtasks: task.subtasks });
    }
  };

  const toggleSubtask = async (taskId: string, subtaskId: string) => {
    const updatedTasks = tasks.map(task => {
      if (task.id === taskId) {
        const updatedSubtasks = task.subtasks?.map(subtask => {
          if (subtask.id === subtaskId) {
            return { ...subtask, completed: !subtask.completed };
          }
          return subtask;
        });
        return { ...task, subtasks: updatedSubtasks };
      }
      return task;
    });

    setTasks(updatedTasks);

    // Update in database
    const task = updatedTasks.find(t => t.id === taskId);
    if (task) {
      await firestoreUserTasks.updateTask(user.id, taskId, { subtasks: task.subtasks });
    }
  };

  // Recurring task functions
  const generateRecurringTask = (originalTask: Task): Task => {
    if (!originalTask.recurrence) return originalTask;

    const { type, interval } = originalTask.recurrence;
    let newDueDate = new Date(originalTask.dueDate);

    switch (type) {
      case 'daily':
        newDueDate = addDays(newDueDate, interval);
        break;
      case 'weekly':
        newDueDate = addWeeks(newDueDate, interval);
        break;
      case 'monthly':
        newDueDate = addMonths(newDueDate, interval);
        break;
    }

    return {
      ...originalTask,
      id: Date.now().toString(),
      status: 'pending',
      dueDate: newDueDate.toISOString().split('T')[0],
      createdAt: new Date().toISOString(),
      subtasks: originalTask.subtasks?.map(st => ({ ...st, completed: false })) || [],
    };
  };

  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        if (searchQuery) {
          setSearchQuery("");
        } else if (showAdvancedFilters) {
          setShowAdvancedFilters(false);
        }
      }
    };

    document.addEventListener("keydown", handleKeyPress);
    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [searchQuery, showAdvancedFilters]);

  useEffect(() => {
    localStorage.setItem("taskManager.priorityFilter", priorityFilter);
  }, [priorityFilter]);

  useEffect(() => {
    localStorage.setItem("taskManager.subjectFilter", subjectFilter);
  }, [subjectFilter]);

  useEffect(() => {
    localStorage.setItem(
      "taskManager.dateRangeFilter",
      JSON.stringify(dateRangeFilter)
    );
  }, [dateRangeFilter]);

  const loadTasks = async () => {
    try {
      const userTasks = await firestoreUserTasks.getTasks(user.id);
      setTasks(sortTasksByDateAndPriority(userTasks));
    } catch (error) {
      console.error("Error loading tasks:", error);
    }
  };

  const resetForm = () => {
    setTaskForm({
      title: "",
      description: "",
      subject: "",
      dueDate: "",
      priority: "medium",
      subtasks: [],
      recurrence: null,
    });
  };

  const handleAddTask = async () => {
    if (!taskForm.title.trim()) {
      alert("Title is required.");
      return;
    }
    if (!taskForm.dueDate) {
      alert("Due Date is required.");
      return;
    }

    const newTask = {
      title: taskForm.title.trim(),
      description: taskForm.description.trim(),
      subject: taskForm.subject.trim(),
      dueDate: taskForm.dueDate,
      priority: taskForm.priority,
      status: "pending" as "pending" | "completed",
      createdAt: new Date().toISOString(),
      subtasks: taskForm.subtasks,
      recurrence: taskForm.recurrence || undefined,
      order: tasks.length,
    };

    try {
      await firestoreUserTasks.addTask(user.id, newTask);
      resetForm();
      setShowAddTask(false);
      await loadTasks();

      // Check achievements
      await checkAchievements('task_created');

      // Dispatch event to update dashboard
      window.dispatchEvent(new CustomEvent("taskCreated", { detail: newTask }));
    } catch (error) {
      console.error("Error adding task:", error);
      alert("Failed to add task. Please try again.");
    }
  };

  const handleEditTask = async () => {
    if (!editingTask) return;

    if (!taskForm.title.trim()) {
      alert("Title is required.");
      return;
    }
    if (!taskForm.dueDate) {
      alert("Due Date is required.");
      return;
    }

    const updates = {
      title: taskForm.title.trim(),
      description: taskForm.description.trim(),
      subject: taskForm.subject.trim(),
      dueDate: taskForm.dueDate,
      priority: taskForm.priority,
    };

    try {
      await firestoreUserTasks.updateTask(user.id, editingTask.id, updates);
      setEditingTask(null);
      resetForm();
      await loadTasks();

      // Dispatch event to update dashboard
      window.dispatchEvent(
        new CustomEvent("taskUpdated", {
          detail: { ...editingTask, ...updates },
        })
      );
    } catch (error) {
      console.error("Error updating task:", error);
      alert("Failed to update task. Please try again.");
    }
  };

  const toggleTaskStatus = async (task: Task) => {
    try {
      const isCompleting = task.status !== "completed";

      // Add rocket animation for completing tasks
      if (isCompleting) {
        // Add haptic feedback for mobile dopamine boost
        if (navigator.vibrate) {
          // Create a satisfying vibration pattern: strong pulse, pause, medium pulse
          navigator.vibrate([200, 100, 300, 100, 400, 50, 200]);
        }

        // Start rocket animation
        setAnimatingTasks((prev) => new Set([...prev, task.id]));

        // Wait for animation to start
        setTimeout(() => {
          // Remove from animating set after animation completes
          setAnimatingTasks((prev) => {
            const newSet = new Set(prev);
            newSet.delete(task.id);
            return newSet;
          });
        }, 1500); // Animation duration

        // Track session progress
        const newSessionCount = completedInSession + 1;
        setCompletedInSession(newSessionCount);

        // Show celebration message with milestone bonuses
        let messages: string[] = [];

        // Special milestone messages (highest priority)
        if (newSessionCount === 3) {
          messages = [
            "🔥 Triple Threat! Your productivity is blazing!",
            "⚡ Lightning Fast! Three victories in a row!",
            "� Hat Trick Hero! Nothing can stop you now!",
          ];
        } else if (newSessionCount === 5) {
          messages = [
            "🌟 Five Star Performance! You're absolutely crushing it!",
            "🚀 Rocket Mode Activated! Five tasks conquered!",
            "💎 Diamond Status Unlocked! Pure excellence!",
          ];
        } else if (newSessionCount >= 10) {
          messages = [
            "👑 Legendary Status! You're the Task Master Supreme!",
            "� Champion Elite! Your focus is unmatched!",
            "⚡ Productivity Goddess/God! Bow to your greatness!",
          ];
        } else if (newSessionCount % 5 === 0) {
          messages = [
            `🎊 Epic Milestone! ${newSessionCount} tasks dominated!`,
            `💪 Power Streak! ${newSessionCount} victories claimed!`,
            `🔥 On Fire! ${newSessionCount} goals obliterated!`,
          ];
        } else {
          // Elegant regular completion messages
          messages = [
            "✨ Magnificent! Another victory claimed!",
            "🎯 Bullseye! Perfect execution achieved!",
            "💫 Stellar Work! You're absolutely shining!",
            "🎭 Masterful! Your skills are incredible!",
            "🌈 Beautiful! Progress painted perfectly!",
            "🎪 Spectacular! What an amazing show!",
            "💎 Brilliant! You're a true gem!",
            "� Artistic! Crafting success beautifully!",
            "🎵 Harmonious! In perfect rhythm now!",
            "🔮 Magical! Making dreams come true!",
            "🎪 Phenomenal! Standing ovation earned!",
            "🌺 Flourishing! Blooming with success!",
          ];
        }

        // Select unique message that hasn't been used recently
        const availableMessages = messages.filter(
          (msg) => !usedMessages.has(msg)
        );
        let selectedMessage: string;

        if (availableMessages.length === 0) {
          // All messages used, reset and pick any
          setUsedMessages(new Set());
          selectedMessage =
            messages[Math.floor(Math.random() * messages.length)];
        } else {
          // Pick from unused messages
          selectedMessage =
            availableMessages[
              Math.floor(Math.random() * availableMessages.length)
            ];
        }

        // Add to used messages
        setUsedMessages((prev) => new Set([...prev, selectedMessage]));

        setCelebrationMessage(selectedMessage);
        setShowCelebration(true);

        // Hide celebration after 2.5 seconds (4 seconds for milestones)
        const celebrationDuration = newSessionCount >= 5 ? 4000 : 2500;
        setTimeout(() => {
          setShowCelebration(false);
        }, celebrationDuration);

        // Enhanced vibration for milestones
        if (navigator.vibrate) {
          if (newSessionCount >= 5) {
            navigator.vibrate([100, 50, 100, 50, 200]); // Special milestone pattern
          } else {
            navigator.vibrate([50, 30, 100]); // Success pattern
          }
        }

        // Create confetti effect
        createCompletionEffect();
      }

      await firestoreUserTasks.updateTask(user.id, task.id, {
        status: task.status === "completed" ? "pending" : "completed",
      });

      // Handle recurring tasks
      if (isCompleting && task.recurrence) {
        const nextTask = generateRecurringTask(task);
        await firestoreUserTasks.addTask(user.id, nextTask);
      }

      await loadTasks();

      // Check achievements
      if (isCompleting) {
        await checkAchievements('task_completed');
      }

      // Dispatch event to update dashboard
      const eventType =
        task.status === "completed" ? "taskUpdated" : "taskCompleted";
      window.dispatchEvent(
        new CustomEvent(eventType, {
          detail: {
            ...task,
            status: task.status === "completed" ? "pending" : "completed",
          },
        })
      );
    } catch (error) {
      console.error("Error toggling task status:", error);
      alert("Failed to update task status. Please try again.");
    }
  };

  const createCompletionEffect = () => {
    // Create a temporary confetti effect
    const container = document.body;
    const colors = [
      "#FF6B6B",
      "#4ECDC4",
      "#45B7D1",
      "#96CEB4",
      "#FFEAA7",
      "#DDA0DD",
    ];

    for (let i = 0; i < 12; i++) {
      const confetti = document.createElement("div");
      confetti.style.cssText = `
        position: fixed;
        width: 8px;
        height: 8px;
        background: ${colors[Math.floor(Math.random() * colors.length)]};
        border-radius: 50%;
        z-index: 10000;
        pointer-events: none;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        animation: confetti-fall 0.8s ease-out forwards;
      `;

      const style = document.createElement("style");
      style.textContent = `
        @keyframes confetti-fall {
          0% {
            transform: translate(-50%, -50%) scale(1) rotate(0deg);
            opacity: 1;
          }
          100% {
            transform: translate(${(Math.random() - 0.5) * 400}px, ${
        Math.random() * 300 + 200
      }px) scale(0) rotate(${Math.random() * 360}deg);
            opacity: 0;
          }
        }
      `;

      if (!document.getElementById("confetti-styles")) {
        style.id = "confetti-styles";
        document.head.appendChild(style);
      }

      container.appendChild(confetti);

      setTimeout(() => {
        if (container.contains(confetti)) {
          container.removeChild(confetti);
        }
      }, 800);
    }
  };

  const deleteTask = async (taskId: string) => {
    if (window.confirm("Are you sure you want to delete this task?")) {
      try {
        // Find the task before deleting for the event
        const taskToDelete = tasks.find((t) => t.id === taskId);

        await firestoreUserTasks.deleteTask(user.id, taskId);
        await loadTasks();

        // Dispatch event to update dashboard
        window.dispatchEvent(
          new CustomEvent("taskDeleted", { detail: taskToDelete })
        );
      } catch (error) {
        console.error("Error deleting task:", error);
        alert("Failed to delete task. Please try again.");
      }
    }
  };

  const startEditing = (task: Task) => {
    setEditingTask(task);
    setTaskForm({
      title: task.title,
      description: task.description,
      subject: task.subject,
      dueDate: task.dueDate,
      priority: task.priority,
      subtasks: task.subtasks || [],
      recurrence: task.recurrence || null,
    });
  };

  const isOverdue = (task: Task) => {
    if (task.status === "completed") return false;
    return isAfter(startOfDay(new Date()), startOfDay(new Date(task.dueDate)));
  };

  const isToday = (task: Task) => {
    const today = startOfDay(new Date());
    const taskDate = startOfDay(new Date(task.dueDate));
    return taskDate.getTime() === today.getTime();
  };

  const isTomorrow = (task: Task) => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowDate = startOfDay(tomorrow);
    const taskDate = startOfDay(new Date(task.dueDate));
    return taskDate.getTime() === tomorrowDate.getTime();
  };

  const sortTasksByDateAndPriority = (tasks: Task[]) => {
    // Separate completed and pending tasks
    const pendingTasks = tasks.filter((task) => task.status !== "completed");
    const completedTasks = tasks.filter((task) => task.status === "completed");

    // Sort pending tasks by date priority (today, tomorrow, others) then by priority
    const sortedPending = pendingTasks.sort((a, b) => {
      const today = startOfDay(new Date());
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);

      const aDate = startOfDay(new Date(a.dueDate));
      const bDate = startOfDay(new Date(b.dueDate));

      const aIsToday = aDate.getTime() === today.getTime();
      const aTomorrow = aDate.getTime() === tomorrow.getTime();
      const bIsToday = bDate.getTime() === today.getTime();
      const bTomorrow = bDate.getTime() === tomorrow.getTime();

      // Priority order: high = 0, medium = 1, low = 2
      const priorityOrder = { high: 0, medium: 1, low: 2 };

      // First priority: Date-based grouping (today first, tomorrow second, others last)
      if (aIsToday && !bIsToday) {
        return -1; // Today tasks come first
      }
      if (bIsToday && !aIsToday) {
        return 1; // Today tasks come first
      }

      if (aTomorrow && !bTomorrow && !bIsToday) {
        return -1; // Tomorrow tasks come second (after today)
      }
      if (bTomorrow && !aTomorrow && !aIsToday) {
        return 1; // Tomorrow tasks come second (after today)
      }

      // Within the same date group, sort by priority first
      const aPriority = priorityOrder[a.priority as keyof typeof priorityOrder];
      const bPriority = priorityOrder[b.priority as keyof typeof priorityOrder];

      if (aPriority !== bPriority) {
        return aPriority - bPriority; // High -> Medium -> Low
      }

      // If same priority within same date group, sort by due date
      return aDate.getTime() - bDate.getTime();
    });

    // Sort completed tasks by completion date (most recently completed first)
    const sortedCompleted = completedTasks.sort((a, b) => {
      // Sort by due date (most recent first)
      return new Date(b.dueDate).getTime() - new Date(a.dueDate).getTime();
    });

    // Return pending tasks first, then completed tasks at bottom
    return [...sortedPending, ...sortedCompleted];
  };

  const getFilteredTasks = () => {
    let filtered = tasks;

    // Apply status filter
    switch (filter) {
      case "pending":
        filtered = filtered.filter((task) => task.status === "pending");
        break;
      case "completed":
        filtered = filtered.filter((task) => task.status === "completed");
        break;
      case "overdue":
        filtered = filtered.filter((task) => isOverdue(task));
        break;
      default:
        break;
    }

    // Apply priority filter
    if (priorityFilter !== "all") {
      filtered = filtered.filter((task) => task.priority === priorityFilter);
    }

    // Apply subject filter
    if (subjectFilter !== "all") {
      filtered = filtered.filter(
        (task) => task.subject.toLowerCase() === subjectFilter.toLowerCase()
      );
    }

    // Apply search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(
        (task) =>
          task.title.toLowerCase().includes(query) ||
          task.description.toLowerCase().includes(query) ||
          task.subject.toLowerCase().includes(query)
      );
    }

    // Apply date range filter
    if (dateRangeFilter.startDate || dateRangeFilter.endDate) {
      filtered = filtered.filter((task) => {
        const taskDate = new Date(task.dueDate);
        const startDate = dateRangeFilter.startDate
          ? new Date(dateRangeFilter.startDate)
          : null;
        const endDate = dateRangeFilter.endDate
          ? new Date(dateRangeFilter.endDate)
          : null;

        if (startDate && taskDate < startDate) return false;
        if (endDate && taskDate > endDate) return false;
        return true;
      });
    }

    // Sort by due date first, then by priority for same date tasks
    return sortTasksByDateAndPriority(filtered);
  };

  const getUniqueSubjects = () => {
    const subjects = tasks
      .map((task) => task.subject)
      .filter((subject) => subject.trim() !== "")
      .filter((subject, index, arr) => arr.indexOf(subject) === index);
    return subjects;
  };

  const clearAllFilters = () => {
    setFilter("all");
    setPriorityFilter("all");
    setSubjectFilter("all");
    setSearchQuery("");
    setDateRangeFilter({ startDate: "", endDate: "" });
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "text-red-700 bg-gradient-to-r from-red-50 to-red-100 border-red-200";
      case "medium":
        return "text-amber-700 bg-gradient-to-r from-amber-50 to-amber-100 border-amber-200";
      case "low":
        return "text-green-700 bg-gradient-to-r from-green-50 to-green-100 border-green-200";
      default:
        return "text-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200";
    }
  };

  const getPriorityIcon = (priority: string) => {
    switch (priority) {
      case "high":
        return <ArrowUp className="w-3 h-3 mr-1.5" />;
      case "medium":
        return <Minus className="w-3 h-3 mr-1.5" />;
      case "low":
        return <ArrowDown className="w-3 h-3 mr-1.5" />;
      default:
        return null;
    }
  };

  const getStatusCounts = () => {
    const filteredTasks = getFilteredTasks();
    const urgentTasks = tasks.filter(
      (t) =>
        (isToday(t) || isTomorrow(t)) &&
        t.priority === "high" &&
        t.status !== "completed" &&
        !isOverdue(t)
    ).length;

    const todayTasks = tasks.filter(
      (t) => isToday(t) && t.status !== "completed"
    ).length;
    const tomorrowTasks = tasks.filter(
      (t) => isTomorrow(t) && t.status !== "completed"
    ).length;

    return {
      all: tasks.length,
      pending: tasks.filter((t) => t.status === "pending").length,
      completed: tasks.filter((t) => t.status === "completed").length,
      overdue: tasks.filter((t) => isOverdue(t)).length,
      filtered: filteredTasks.length,
      urgent: urgentTasks,
      today: todayTasks,
      tomorrow: tomorrowTasks,
    };
  };

  const counts = getStatusCounts();

  // Swipeable Task Item Component
  // const SwipeableTaskItem: React.FC<{ task: Task }> = ({ task }) => {
    const [translateX, setTranslateX] = useState(0);
    const [isDragging, setIsDragging] = useState(false);
    const [startX, setStartX] = useState(0);
    const itemRef = useRef<HTMLDivElement>(null);

    const handleTouchStart = (e: React.TouchEvent) => {
      setIsDragging(true);
      setStartX(e.touches[0].clientX);
    };

    const handleTouchMove = (e: React.TouchEvent) => {
      if (!isDragging) return;
      const currentX = e.touches[0].clientX;
      const diff = currentX - startX;
      setTranslateX(Math.max(-200, Math.min(200, diff)));
    };

    const handleTouchEnd = () => {
      setIsDragging(false);

      if (translateX > 80) {
        // Right swipe - Complete task
        toggleTaskStatus(task);
        setTranslateX(0);
        // Add visual feedback
        if (navigator.vibrate) {
          navigator.vibrate(50);
        }
      } else if (translateX < -80) {
        // Left swipe - Delete task
        deleteTask(task.id);
        setTranslateX(0);
        // Add visual feedback
        if (navigator.vibrate) {
          navigator.vibrate([50, 50, 50]);
        }
      } else {
        // Reset position
        setTranslateX(0);
      }
    };

    const handleMouseDown = (e: React.MouseEvent) => {
      setIsDragging(true);
      setStartX(e.clientX);
    };

    const handleMouseMove = (e: React.MouseEvent) => {
      if (!isDragging) return;
      const diff = e.clientX - startX;
      setTranslateX(Math.max(-200, Math.min(200, diff)));
    };

    const handleMouseUp = () => {
      if (!isDragging) return;
      setIsDragging(false);

      if (translateX > 80) {
        toggleTaskStatus(task);
        setTranslateX(0);
      } else if (translateX < -80) {
        deleteTask(task.id);
        setTranslateX(0);
      } else {
        setTranslateX(0);
      }
    };

    return (
      <div className="relative overflow-hidden">
        {/* Background Actions - Hidden for completed tasks */}
        {task.status !== "completed" && (
          <div className="absolute inset-0 flex rounded-xl overflow-hidden">
            {/* Complete Action (Right Swipe) */}
            <div
              className={`flex-1 flex items-center justify-start pl-4 sm:pl-6 transition-all duration-300 ${
                translateX > 50
                  ? "bg-gradient-to-r from-emerald-500 via-green-500 to-teal-500 shadow-2xl animate-pulse"
                  : translateX > 30
                  ? "bg-gradient-to-r from-green-500 to-emerald-600 shadow-lg"
                  : "bg-gradient-to-r from-green-400 to-green-500"
              }`}
            >
              <div
                className={`flex items-center text-white transform transition-all duration-200 ${
                  translateX > 50
                    ? "scale-110 sm:scale-125 animate-bounce"
                    : translateX > 30
                    ? "scale-105 sm:scale-110"
                    : "scale-100"
                } ${translateX > 50 ? "opacity-100" : "opacity-90"}`}
              >
                <Check
                  className={`w-5 h-5 sm:w-6 sm:h-6 mr-1.5 sm:mr-2 drop-shadow-sm flex-shrink-0 ${
                    translateX > 50 ? "animate-spin" : ""
                  }`}
                />
                <span
                  className={`font-medium text-sm sm:text-base whitespace-nowrap ${
                    translateX > 50 ? "font-bold animate-pulse" : ""
                  }`}
                >
                  Complete
                </span>
              </div>
            </div>
            {/* Delete Action (Left Swipe) */}
            <div
              className={`flex-1 flex items-center justify-end pr-4 sm:pr-6 transition-all duration-300 ${
                translateX < -30
                  ? "bg-gradient-to-l from-red-500 to-red-600 shadow-lg"
                  : "bg-gradient-to-l from-red-400 to-red-500"
              }`}
            >
              <div
                className={`flex items-center text-white transform transition-all duration-200 ${
                  translateX < -30 ? "scale-105 sm:scale-110" : "scale-100"
                } ${translateX < -50 ? "opacity-100" : "opacity-90"}`}
              >
                <span className="font-medium text-sm sm:text-base whitespace-nowrap mr-1.5 sm:mr-2">
                  Delete
                </span>
                <Trash2 className="w-5 h-5 sm:w-6 sm:h-6 drop-shadow-sm flex-shrink-0" />
              </div>
            </div>
          </div>
        )}

        {/* Task Content */}
        <div
          ref={itemRef}
          className={`transform transition-all duration-300 ${
            animatingTasks.has(task.id)
              ? "animate-task-rocket pointer-events-none border-2 border-blue-400 shadow-rocket bg-gradient-to-t from-blue-100 via-white to-blue-300" // Rocket effect
              : task.status === "completed"
              ? "bg-gradient-to-r from-gray-50 to-gray-100 border-gray-300 shadow-sm opacity-60 scale-95"
              : isOverdue(task)
              ? "bg-gradient-to-r from-red-50 to-red-100 border-red-300 shadow-md"
              : "bg-gradient-to-r from-white to-gray-50 border-gray-200 shadow-lg hover:shadow-xl"
          } border rounded-xl p-5 hover:border-blue-200 backdrop-blur-sm`}
          style={{
            transform: `translateX(${translateX}px)`,
            transition: isDragging
              ? "none"
              : "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
            boxShadow: isDragging
              ? "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
              : undefined,
            // Override any background for completed tasks to prevent swipe colors
            ...(task.status === "completed" &&
              !animatingTasks.has(task.id) && {
                background:
                  "linear-gradient(to right, rgb(249 250 251), rgb(243 244 246))",
                borderColor: "rgb(209 213 219)",
              }),
          }}
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          onMouseDown={handleMouseDown}
          onMouseMove={handleMouseMove}
          onMouseUp={handleMouseUp}
          onMouseLeave={() => {
            if (isDragging) {
              handleMouseUp();
            }
          }}
        >
          <div className="flex items-start space-x-4">
            <button
              onClick={() => toggleTaskStatus(task)}
              className={`mt-1 p-1 rounded-full transition-all duration-300 transform ${
                task.status === "completed"
                  ? "text-green-600 hover:text-green-700 hover:bg-green-50 scale-110 animate-pulse"
                  : "text-blue-600 hover:text-blue-700 hover:bg-blue-50 hover:shadow-lg"
              } hover:scale-125 active:scale-95 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50`}
              title={
                task.status === "completed"
                  ? "Mark as pending"
                  : "Mark as completed"
              }
            >
              {task.status === "completed" ? (
                <CheckCircle2 className="w-6 h-6 drop-shadow-sm" />
              ) : (
                <Circle className="w-6 h-6 hover:fill-blue-100 transition-all duration-200" />
              )}
            </button>

            <div className="flex-1 min-w-0">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h3
                    className={`font-semibold text-lg leading-tight ${
                      task.status === "completed"
                        ? "text-gray-500 line-through"
                        : "text-gray-800"
                    }`}
                  >
                    {task.title}
                    {isOverdue(task) && (
                      <AlertTriangle className="inline w-5 h-5 text-red-500 ml-2 animate-pulse" />
                    )}
                    {(isToday(task) || isTomorrow(task)) &&
                      task.priority === "high" &&
                      !isOverdue(task) && (
                        <span className="inline-flex items-center ml-2 px-2 py-0.5 bg-gradient-to-r from-red-500 to-red-600 text-white text-xs font-bold rounded-full animate-pulse">
                          ⚡ URGENT
                        </span>
                      )}
                    {isToday(task) &&
                      task.priority !== "high" &&
                      !isOverdue(task) && (
                        <span className="inline-flex items-center ml-2 px-2 py-0.5 bg-gradient-to-r from-blue-500 to-blue-600 text-white text-xs font-medium rounded-full">
                          📅 TODAY
                        </span>
                      )}
                    {isTomorrow(task) &&
                      task.priority !== "high" &&
                      !isOverdue(task) && (
                        <span className="inline-flex items-center ml-2 px-2 py-0.5 bg-gradient-to-r from-purple-500 to-purple-600 text-white text-xs font-medium rounded-full">
                          🔜 TOMORROW
                        </span>
                      )}
                  </h3>
                  {task.description && (
                    <p
                      className={`text-sm mt-2 leading-relaxed ${
                        task.status === "completed"
                          ? "text-gray-400"
                          : "text-gray-600"
                      }`}
                    >
                      {task.description}
                    </p>
                  )}
                </div>

                <div className="flex items-center space-x-1 ml-4">
                  <button
                    onClick={() => startEditing(task)}
                    className="p-2 rounded-full text-gray-400 hover:text-blue-600 hover:bg-blue-50 transition-all duration-200 hover:scale-105"
                  >
                    <Edit3 className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => deleteTask(task.id)}
                    className="p-2 rounded-full text-gray-400 hover:text-red-600 hover:bg-red-50 transition-all duration-200 hover:scale-105"
                  >
                    <Trash2 className="w-4 h-4" />
                  </button>
                </div>
              </div>

              <div className="flex items-center flex-wrap gap-3 mt-4">
                {task.subject && (
                  <span className="px-3 py-1.5 bg-gradient-to-r from-blue-100 to-blue-200 text-blue-700 text-xs font-medium rounded-full border border-blue-200">
                    {task.subject}
                  </span>
                )}
                <span
                  className={`flex items-center px-3 py-1.5 text-xs font-medium rounded-full border ${getPriorityColor(
                    task.priority
                  )}`}
                >
                  {getPriorityIcon(task.priority)}
                  {task.priority} priority
                </span>
                <div
                  className={`flex items-center px-3 py-1.5 rounded-full text-xs font-medium border ${
                    isOverdue(task) && task.status !== "completed"
                      ? "text-red-700 bg-gradient-to-r from-red-50 to-red-100 border-red-200"
                      : isToday(task)
                      ? "text-blue-700 bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200"
                      : isTomorrow(task)
                      ? "text-purple-700 bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200"
                      : "text-gray-600 bg-gradient-to-r from-gray-50 to-gray-100 border-gray-200"
                  }`}
                >
                  <Calendar className="w-3 h-3 mr-1.5" />
                  {isToday(task) ? (
                    <span className="font-semibold">Today</span>
                  ) : isTomorrow(task) ? (
                    <span className="font-semibold">Tomorrow</span>
                  ) : (
                    format(new Date(task.dueDate), "MMM dd, yyyy")
                  )}
                  {(isToday(task) || isTomorrow(task)) && (
                    <span className="ml-2 text-xs opacity-75">
                      ({format(new Date(task.dueDate), "MMM dd")})
                    </span>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Task completion feedback - no overlay needed as card is the rocket */}
      </div>
    );
  };

  // Enhanced Task Item Component with Subtasks

  // Achievements Panel Component
  // ...existing code...

  // ...existing code...
      <div className="bg-white h-full flex flex-col">
        {/* Header */}
        <div className="border-b border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900">To-Do List</h2>
              {tasks.length > 0 && (
                <div className="mt-1">
                  <p className="text-sm text-gray-600">
                    {getFilteredTasks().length === tasks.length
                      ? `${tasks.length} ${
                          tasks.length === 1 ? "task" : "tasks"
                        } total`
                      : `${getFilteredTasks().length} of ${
                          tasks.length
                        } tasks shown`}
                  </p>
                  {(counts.urgent > 0 ||
                    counts.today > 0 ||
                    counts.tomorrow > 0) && (
                    <div className="flex items-center gap-3 mt-1">
                      {counts.urgent > 0 && (
                        <span className="flex items-center px-2 py-1 bg-red-100 text-red-700 text-xs font-medium rounded-full">
                          ⚡ {counts.urgent} urgent
                        </span>
                      )}
                      {counts.today > 0 && (
                        <span className="flex items-center px-2 py-1 bg-blue-100 text-blue-700 text-xs font-medium rounded-full">
                          📅 {counts.today} today
                        </span>
                      )}
                      {counts.tomorrow > 0 && (
                        <span className="flex items-center px-2 py-1 bg-purple-100 text-purple-700 text-xs font-medium rounded-full">
                          🔜 {counts.tomorrow} tomorrow
                        </span>
                      )}
                    </div>
                  )}
                  <p className="text-xs text-gray-500 mt-1 flex items-center flex-wrap">
                    <span className="flex items-center mr-3">
                      <Calendar className="w-3 h-3 mr-1 text-blue-500" />
                      <span className="font-medium text-blue-600">
                        Today/Tomorrow:
                      </span>
                      <ArrowUp className="w-3 h-3 mx-1 text-red-500" />
                      <Minus className="w-3 h-3 mx-1 text-amber-500" />
                      <ArrowDown className="w-3 h-3 mr-1 text-green-500" />
                      <span>then date</span>
                    </span>
                    <span className="flex items-center">
                      <ArrowUp className="w-3 h-3 mr-1 text-red-500" />
                      <Minus className="w-3 h-3 mr-1 text-amber-500" />
                      <ArrowDown className="w-3 h-3 mr-1 text-green-500" />
                      <span>Others: priority first, then date</span>
                    </span>
                  </p>
                </div>
              )}
            </div>
            <button
              onClick={() => setShowAddTask(true)}
              className="flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </button>
          </div>

          {/* Filter Tabs */}
          <div className="space-y-4">
            {/* Basic Status Filters */}
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "all", label: "All", count: counts.all },
                { key: "pending", label: "Pending", count: counts.pending },
                {
                  key: "completed",
                  label: "Completed",
                  count: counts.completed,
                },
                { key: "overdue", label: "Overdue", count: counts.overdue },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setFilter(tab.key as any)}
                  className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    filter === tab.key
                      ? "bg-white text-gray-900 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                  <span
                    className={`ml-2 px-2 py-0.5 text-xs rounded-full ${
                      tab.key === "overdue" && tab.count > 0
                        ? "bg-red-100 text-red-600"
                        : "bg-gray-200 text-gray-600"
                    }`}
                  >
                    {tab.count}
                  </span>
                </button>
              ))}
            </div>

            {/* Advanced Filters Toggle and Search */}
            <div className="flex items-center space-x-4">
              {/* Search Input */}
              <div className="flex-1 relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Search tasks by title, description, or subject..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                {searchQuery && (
                  <button
                    onClick={() => setSearchQuery("")}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                  >
                    <X className="w-4 h-4" />
                  </button>
                )}
              </div>

              {/* Advanced Filters Toggle */}
              <button
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                className={`flex items-center px-4 py-2 rounded-lg border transition-colors ${
                  showAdvancedFilters ||
                  priorityFilter !== "all" ||
                  subjectFilter !== "all" ||
                  dateRangeFilter.startDate ||
                  dateRangeFilter.endDate
                    ? "bg-blue-50 border-blue-200 text-blue-700"
                    : "bg-white border-gray-200 text-gray-600 hover:text-gray-900"
                }`}
              >
                <Filter className="w-4 h-4 mr-2" />
                Filters
                {(priorityFilter !== "all" ||
                  subjectFilter !== "all" ||
                  dateRangeFilter.startDate ||
                  dateRangeFilter.endDate) && (
                  <span className="ml-1 px-2 py-0.5 text-xs bg-blue-100 text-blue-700 rounded-full">
                    Active
                  </span>
                )}
              </button>
            </div>

            {/* Advanced Filters Panel */}
            {showAdvancedFilters && (
              <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Priority Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority
                    </label>
                    <select
                      value={priorityFilter}
                      onChange={(e) => setPriorityFilter(e.target.value as any)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">All Priorities</option>
                      <option value="high">High Priority</option>
                      <option value="medium">Medium Priority</option>
                      <option value="low">Low Priority</option>
                    </select>
                  </div>

                  {/* Subject Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subject
                    </label>
                    <select
                      value={subjectFilter}
                      onChange={(e) => setSubjectFilter(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="all">All Subjects</option>
                      {getUniqueSubjects().map((subject) => (
                        <option key={subject} value={subject}>
                          {subject}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Date Range Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Due Date Range
                    </label>
                    <div className="space-y-2">
                      <input
                        type="date"
                        value={dateRangeFilter.startDate}
                        onChange={(e) =>
                          setDateRangeFilter({
                            ...dateRangeFilter,
                            startDate: e.target.value,
                          })
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        placeholder="Start date"
                      />
                      <input
                        type="date"
                        value={dateRangeFilter.endDate}
                        onChange={(e) =>
                          setDateRangeFilter({
                            ...dateRangeFilter,
                            endDate: e.target.value,
                          })
                        }
                        className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                        placeholder="End date"
                      />
                    </div>
                  </div>
                </div>

                {/* Clear Filters Button */}
                {(priorityFilter !== "all" ||
                  subjectFilter !== "all" ||
                  searchQuery ||
                  dateRangeFilter.startDate ||
                  dateRangeFilter.endDate) && (
                  <div className="mt-4 flex justify-end">
                    <button
                      onClick={clearAllFilters}
                      className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      Clear All Filters
                    </button>
                  </div>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Task List */}
        <div className="flex-1 overflow-auto p-6">
          {/* Active Filters Summary */}
          {(searchQuery ||
            priorityFilter !== "all" ||
            subjectFilter !== "all" ||
            dateRangeFilter.startDate ||
            dateRangeFilter.endDate) && (
            <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-xl">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium text-blue-900">Active Filters</h4>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {searchQuery && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                        Search: "{searchQuery}"
                      </span>
                    )}
                    {priorityFilter !== "all" && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                        Priority: {priorityFilter}
                      </span>
                    )}
                    {subjectFilter !== "all" && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                        Subject: {subjectFilter}
                      </span>
                    )}
                    {(dateRangeFilter.startDate || dateRangeFilter.endDate) && (
                      <span className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full">
                        Date: {dateRangeFilter.startDate || "..."} -{" "}
                        {dateRangeFilter.endDate || "..."}
                      </span>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-blue-700">
                    <span className="font-semibold">{counts.filtered}</span> of{" "}
                    <span className="font-semibold">{counts.all}</span> tasks
                  </p>
                  <button
                    onClick={clearAllFilters}
                    className="text-xs text-blue-600 hover:text-blue-800 underline mt-1"
                  >
                    Clear all
                  </button>
                </div>
              </div>
            </div>
          )}

          {getFilteredTasks().length > 0 && (
            <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl shadow-sm">
              <p className="text-sm text-blue-800 text-center font-medium">
                ✨ <strong>Pro tip:</strong> Swipe right to complete, swipe left
                to delete
              </p>
            </div>
          )}
          <div className="space-y-4">
            {(() => {
              const allTasks = getFilteredTasks();
              const pendingTasks = allTasks.filter(
                (task) => task.status !== "completed"
              );
              const completedTasks = allTasks.filter(
                (task) => task.status === "completed"
              );

              return (
                <>
                  {/* Pending Tasks */}
                  {pendingTasks.map((task) => (
                    <div key={task.id} className="mb-3">
                      <EnhancedTaskItem task={task} />
                    </div>
                  ))}

                  {/* Completed Tasks Section */}
                  {completedTasks.length > 0 && (
                    <>
                      {pendingTasks.length > 0 && (
                        <div className="flex items-center my-8 px-4">
                          <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
                          <div className="px-4 py-2 bg-gradient-to-r from-green-50 to-emerald-50 rounded-full border border-green-200">
                            <span className="text-sm font-medium text-green-700 flex items-center">
                              <CheckCircle2 className="w-4 h-4 mr-2" />
                              Completed ({completedTasks.length})
                            </span>
                          </div>
                          <div className="flex-1 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
                        </div>
                      )}

                      {/* Completed Tasks - Non-draggable */}
                      {completedTasks.map((task) => (
                        <div key={task.id} className="mb-3">
                          <EnhancedTaskItem task={task} />
                        </div>
                      ))}
                    </>
                  )}
                </>
              );
            })()}
          </div>

          {getFilteredTasks().length === 0 && (
            <div className="text-center py-12">
              <CheckCircle2 className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {tasks.length === 0 ? "No tasks yet" : "No matching tasks"}
              </h3>
              <p className="text-gray-600 mb-6">
                {tasks.length === 0
                  ? "Create your first task to get started with organized studying"
                  : searchQuery ||
                    priorityFilter !== "all" ||
                    subjectFilter !== "all" ||
                    dateRangeFilter.startDate ||
                    dateRangeFilter.endDate
                  ? "Try adjusting your filters to see more tasks"
                  : `You don't have any ${filter} tasks at the moment`}
              </p>
              {tasks.length === 0 ? (
                <button
                  onClick={() => setShowAddTask(true)}
                  className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  <Plus className="w-5 h-5 mr-2" />
                  Add Your First Task
                </button>
              ) : (
                (searchQuery ||
                  priorityFilter !== "all" ||
                  subjectFilter !== "all" ||
                  dateRangeFilter.startDate ||
                  dateRangeFilter.endDate) && (
                  <button
                    onClick={clearAllFilters}
                    className="inline-flex items-center px-6 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 rounded-lg transition-colors"
                  >
                    <X className="w-5 h-5 mr-2" />
                    Clear All Filters
                  </button>
                )
              )}
            </div>
          )}
        </div>

        {/* Add/Edit Task Modal */}
        {(showAddTask || editingTask) && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg w-full max-w-lg">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold">
                  {editingTask ? "Edit Task" : "Add New Task"}
                </h3>
              </div>

              <div className="p-6 space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Title *
                  </label>
                  <input
                    type="text"
                    value={taskForm.title}
                    onChange={(e) =>
                      setTaskForm({ ...taskForm, title: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter task title"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={taskForm.description}
                    onChange={(e) =>
                      setTaskForm({ ...taskForm, description: e.target.value })
                    }
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter task description"
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subject
                    </label>
                    <input
                      type="text"
                      value={taskForm.subject}
                      onChange={(e) =>
                        setTaskForm({ ...taskForm, subject: e.target.value })
                      }
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="e.g., Mathematics"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority
                    </label>
                    <select
                      value={taskForm.priority}
                      onChange={(e) =>
                        setTaskForm({
                          ...taskForm,
                          priority: e.target.value as any,
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Due Date *
                  </label>
                  <input
                    type="date"
                    value={taskForm.dueDate}
                    onChange={(e) =>
                      setTaskForm({ ...taskForm, dueDate: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-200 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  />
                </div>
              </div>

              <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
                <button
                  onClick={() => {
                    if (editingTask) {
                      setEditingTask(null);
                    } else {
                      setShowAddTask(false);
                    }
                    resetForm();
                  }}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={editingTask ? handleEditTask : handleAddTask}
                  disabled={!taskForm.title.trim() || !taskForm.dueDate}
                  className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {editingTask ? "Update" : "Add"} Task
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Elegant Celebration Toast */}
        {showCelebration && (
          <div
            className={`fixed top-4 left-1/2 transform -translate-x-1/2 z-[60] ${
              completedInSession >= 10
                ? "animate-bounce"
                : completedInSession >= 5
                ? "animate-pulse"
                : completedInSession === 3
                ? "animate-bounce"
                : "animate-pulse"
            }`}
          >
            <div
              className={`text-white px-8 py-4 rounded-2xl shadow-2xl border-2 transition-all duration-500 backdrop-blur-sm ${
                completedInSession >= 10
                  ? "bg-gradient-to-r from-purple-600 via-pink-600 to-indigo-600 border-yellow-300 animate-pulse shadow-purple-500/50"
                  : completedInSession >= 5
                  ? "bg-gradient-to-r from-yellow-500 via-orange-500 to-red-500 border-yellow-300 shadow-yellow-500/50"
                  : completedInSession === 3
                  ? "bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 border-pink-300 shadow-blue-500/50"
                  : "bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 border-emerald-300 shadow-emerald-500/50"
              }`}
            >
              <div className="flex items-center justify-center space-x-3">
                {completedInSession >= 10 && (
                  <span className="text-3xl animate-bounce">👑</span>
                )}
                {completedInSession >= 5 && completedInSession < 10 && (
                  <span className="text-2xl animate-spin">🌟</span>
                )}
                {completedInSession === 3 && (
                  <span className="text-2xl animate-pulse">🔥</span>
                )}
                <span
                  className={`font-bold tracking-wide text-center ${
                    completedInSession >= 10
                      ? "text-xl animate-pulse bg-clip-text text-transparent bg-gradient-to-r from-yellow-200 to-pink-200"
                      : completedInSession >= 5
                      ? "text-lg animate-pulse"
                      : completedInSession === 3
                      ? "text-lg"
                      : "text-base"
                  }`}
                >
                  {celebrationMessage}
                </span>
                {completedInSession >= 5 && (
                  <span className="text-2xl animate-spin">✨</span>
                )}
                {completedInSession < 5 && (
                  <span className="text-xl animate-bounce">🎯</span>
                )}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Enhanced Add Task Form */}
      {showAddTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Add New Task</h3>
                <button
                  onClick={() => setShowAddTask(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>

              <form onSubmit={handleAddTask} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Task Title *
                  </label>
                  <input
                    type="text"
                    value={newTask.title}
                    onChange={(e) =>
                      setNewTask({ ...newTask, title: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter task title..."
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Description
                  </label>
                  <textarea
                    value={newTask.description}
                    onChange={(e) =>
                      setNewTask({ ...newTask, description: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                    placeholder="Task description..."
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Priority
                    </label>
                    <select
                      value={newTask.priority}
                      onChange={(e) =>
                        setNewTask({
                          ...newTask,
                          priority: e.target.value as "low" | "medium" | "high",
                        })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="low">Low</option>
                      <option value="medium">Medium</option>
                      <option value="high">High</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Subject
                    </label>
                    <input
                      type="text"
                      value={newTask.subject}
                      onChange={(e) =>
                        setNewTask({ ...newTask, subject: e.target.value })
                      }
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      placeholder="Math, Science, etc."
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Due Date *
                  </label>
                  <input
                    type="date"
                    value={newTask.dueDate}
                    onChange={(e) =>
                      setNewTask({ ...newTask, dueDate: e.target.value })
                    }
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  />
                </div>

                {/* Recurring Task Options */}
                <div>
                  <div className="flex items-center space-x-2 mb-2">
                    <input
                      type="checkbox"
                      id="isRecurring"
                      checked={!!newTask.recurrence}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setNewTask({
                            ...newTask,
                            recurrence: { type: 'daily', interval: 1 }
                          });
                        } else {
                          setNewTask({
                            ...newTask,
                            recurrence: undefined
                          });
                        }
                      }}
                      className="rounded text-blue-600"
                    />
                    <label htmlFor="isRecurring" className="text-sm font-medium text-gray-700 flex items-center">
                      <Repeat className="w-4 h-4 mr-1" />
                      Make this a recurring task
                    </label>
                  </div>

                  {newTask.recurrence && (
                    <div className="grid grid-cols-2 gap-3 mt-3 p-3 bg-gray-50 rounded-lg">
                      <div>
                        <label className="block text-sm text-gray-600 mb-1">Frequency</label>
                        <select
                          value={newTask.recurrence?.type || 'daily'}
                          onChange={(e) =>
                            setNewTask({
                              ...newTask,
                              recurrence: {
                                ...newTask.recurrence!,
                                type: e.target.value as 'daily' | 'weekly' | 'monthly'
                              }
                            })
                          }
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                        >
                          <option value="daily">Daily</option>
                          <option value="weekly">Weekly</option>
                          <option value="monthly">Monthly</option>
                        </select>
                      </div>
                      <div>
                        <label className="block text-sm text-gray-600 mb-1">Every</label>
                        <input
                          type="number"
                          min="1"
                          max="30"
                          value={newTask.recurrence?.interval || 1}
                          onChange={(e) =>
                            setNewTask({
                              ...newTask,
                              recurrence: {
                                ...newTask.recurrence!,
                                interval: parseInt(e.target.value) || 1
                              }
                            })
                          }
                          className="w-full px-2 py-1 text-sm border border-gray-300 rounded"
                        />
                      </div>
                    </div>
                  )}
                </div>

                {/* Initial Subtasks */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2 flex items-center">
                    <List className="w-4 h-4 mr-1" />
                    Initial Subtasks (optional)
                  </label>
                  {(newTask.subtasks || []).map((subtask, index) => (
                    <div key={index} className="flex items-center space-x-2 mb-2">
                      <input
                        type="text"
                        value={subtask.title}
                        onChange={(e) => {
                          const updatedSubtasks = [...(newTask.subtasks || [])];
                          updatedSubtasks[index] = { ...subtask, title: e.target.value };
                          setNewTask({ ...newTask, subtasks: updatedSubtasks });
                        }}
                        className="flex-1 px-2 py-1 text-sm border border-gray-300 rounded focus:ring-1 focus:ring-blue-500"
                        placeholder="Subtask title..."
                      />
                      <button
                        type="button"
                        onClick={() => {
                          const updatedSubtasks = (newTask.subtasks || []).filter((_, i) => i !== index);
                          setNewTask({ ...newTask, subtasks: updatedSubtasks });
                        }}
                        className="text-red-500 hover:text-red-700 p-1"
                      >
                        <X className="w-4 h-4" />
                      </button>
                    </div>
                  ))}
                  <button
                    type="button"
                    onClick={() => {
                      const newSubtask: SubTask = {
                        id: Date.now().toString(),
                        title: '',
                        completed: false,
                        createdAt: new Date().toISOString()
                      };
                      setNewTask({
                        ...newTask,
                        subtasks: [...(newTask.subtasks || []), newSubtask]
                      });
                    }}
                    className="text-blue-600 hover:text-blue-700 text-sm flex items-center mt-2"
                  >
                    <Plus className="w-4 h-4 mr-1" />
                    Add Subtask
                  </button>
                </div>

                <div className="flex space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={() => setShowAddTask(false)}
                    className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Add Task
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}

      {/* Achievements Panel */}
      {showAchievements && <AchievementsPanel />}

  );
}

export default TaskManager;

export default TaskManager;
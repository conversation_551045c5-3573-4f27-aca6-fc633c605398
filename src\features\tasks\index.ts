// Task Management Feature Exports
// This file serves as the main entry point for the task management feature

// Hooks
export { useTasks } from './hooks/useTasks';
export { useTaskFilters } from './hooks/useTaskFilters';
export { useTaskForm } from './hooks/useTaskForm';
export { useTaskAchievements } from './hooks/useTaskAchievements';
export { useTaskStats } from './hooks/useTaskStats';

// Components
export { TaskManager } from './components/TaskManager';
export { TaskCard } from './components/TaskCard';
export { TaskForm } from './components/TaskForm';
export { TaskList } from './components/TaskList';
export { TaskFilters } from './components/TaskFilters';
export { TaskStats } from './components/TaskStats';

// Services
export { taskService } from './services/taskService';
export { achievementService } from './services/achievementService';

// Utils
export * from './utils/taskUtils';
export * from './utils/taskValidation';

// Types (re-export from main types)
export type {
  Task,
  TaskStatus,
  TaskPriority,
  TaskFilter,
  TaskFormData,
  TaskFilters,
  TaskStats,
  SubTask,
  RecurrencePattern,
  Achievement
} from '../../types';

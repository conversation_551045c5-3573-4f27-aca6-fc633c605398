import React from "react";
import { X, Plus, Repeat, List } from "lucide-react";
import { SubTask, RecurrencePattern } from "../types";

interface TaskFormModalProps {
  show: boolean;
  isEdit: boolean;
  taskForm: any;
  onChange: (field: string, value: any) => void;
  onSubmit: () => void;
  onCancel: () => void;
}

const TaskFormModal: React.FC<TaskFormModalProps> = ({
  show,
  isEdit,
  taskForm,
  onChange,
  onSubmit,
  onCancel,
}) => {
  if (!show) return null;
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-lg">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold">
            {isEdit ? "Edit Task" : "Add New Task"}
          </h3>
        </div>
        <div className="p-6 space-y-4">
          {/* ...existing code for form fields, use onChange for updates... */}
        </div>
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onCancel}
            className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onSubmit}
            disabled={!taskForm.title.trim() || !taskForm.dueDate}
            className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isEdit ? "Update" : "Add"} Task
          </button>
        </div>
      </div>
    </div>
  );
};

export default TaskFormModal;

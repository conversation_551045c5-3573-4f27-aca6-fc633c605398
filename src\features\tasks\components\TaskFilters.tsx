import React from 'react';
import { Filter, X, Calendar, Tag, Flag } from 'lucide-react';
import { TaskFilters as TaskFiltersType, TaskFilter, TaskPriority, Task } from '../../../types';
import { getUniqueSubjects, getUniqueTags } from '../utils/taskUtils';

interface TaskFiltersProps {
  filters: TaskFiltersType;
  updateFilter: <K extends keyof TaskFiltersType>(key: K, value: TaskFiltersType[K]) => void;
  clearFilters: () => void;
  getFilterCounts: () => Record<TaskFilter, number>;
  tasks: Task[];
  className?: string;
}

export const TaskFilters: React.FC<TaskFiltersProps> = ({
  filters,
  updateFilter,
  clearFilters,
  getFilterCounts,
  tasks,
  className = '',
}) => {
  const filterCounts = getFilterCounts();
  const uniqueSubjects = getUniqueSubjects(tasks);
  const uniqueTags = getUniqueTags(tasks);

  const statusFilters: { key: TaskFilter; label: string }[] = [
    { key: 'all', label: 'All' },
    { key: 'pending', label: 'Pending' },
    { key: 'completed', label: 'Completed' },
    { key: 'overdue', label: 'Overdue' },
    { key: 'today', label: 'Today' },
    { key: 'tomorrow', label: 'Tomorrow' },
    { key: 'this-week', label: 'This Week' },
  ];

  const priorityFilters: { key: TaskPriority | 'all'; label: string }[] = [
    { key: 'all', label: 'All Priorities' },
    { key: 'urgent', label: 'Urgent' },
    { key: 'high', label: 'High' },
    { key: 'medium', label: 'Medium' },
    { key: 'low', label: 'Low' },
  ];

  const hasActiveFilters = 
    filters.status !== 'all' ||
    filters.priority !== 'all' ||
    filters.subject !== '' ||
    filters.search !== '' ||
    filters.tags.length > 0 ||
    filters.dateRange.startDate !== '' ||
    filters.dateRange.endDate !== '';

  return (
    <div className={`bg-gray-50 border-b border-gray-200 p-6 ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-900 flex items-center gap-2">
          <Filter className="w-5 h-5" />
          Filters
        </h3>
        
        {hasActiveFilters && (
          <button
            onClick={clearFilters}
            className="text-sm text-blue-600 hover:text-blue-800 flex items-center gap-1"
          >
            <X className="w-4 h-4" />
            Clear All
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Status Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Status
          </label>
          <div className="flex flex-wrap gap-2">
            {statusFilters.map(({ key, label }) => (
              <button
                key={key}
                onClick={() => updateFilter('status', key)}
                className={`px-3 py-1.5 text-sm rounded-full border transition-colors ${
                  filters.status === key
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {label}
                {filterCounts[key] > 0 && (
                  <span className="ml-1 text-xs">({filterCounts[key]})</span>
                )}
              </button>
            ))}
          </div>
        </div>

        {/* Priority Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Flag className="w-4 h-4 inline mr-1" />
            Priority
          </label>
          <select
            value={filters.priority}
            onChange={(e) => updateFilter('priority', e.target.value as TaskPriority | 'all')}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            {priorityFilters.map(({ key, label }) => (
              <option key={key} value={key}>
                {label}
              </option>
            ))}
          </select>
        </div>

        {/* Subject Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Tag className="w-4 h-4 inline mr-1" />
            Subject
          </label>
          <select
            value={filters.subject}
            onChange={(e) => updateFilter('subject', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">All Subjects</option>
            {uniqueSubjects.map((subject) => (
              <option key={subject} value={subject}>
                {subject}
              </option>
            ))}
          </select>
        </div>

        {/* Date Range Filter */}
        <div className="lg:col-span-2">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Calendar className="w-4 h-4 inline mr-1" />
            Date Range
          </label>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <input
                type="date"
                value={filters.dateRange.startDate}
                onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, startDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="Start date"
              />
            </div>
            <div>
              <input
                type="date"
                value={filters.dateRange.endDate}
                onChange={(e) => updateFilter('dateRange', { ...filters.dateRange, endDate: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                placeholder="End date"
              />
            </div>
          </div>
        </div>

        {/* Tags Filter */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Tags
          </label>
          <div className="space-y-2 max-h-32 overflow-y-auto">
            {uniqueTags.map((tag) => (
              <label key={tag} className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={filters.tags.includes(tag)}
                  onChange={(e) => {
                    if (e.target.checked) {
                      updateFilter('tags', [...filters.tags, tag]);
                    } else {
                      updateFilter('tags', filters.tags.filter(t => t !== tag));
                    }
                  }}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-700">{tag}</span>
              </label>
            ))}
          </div>
        </div>
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <div className="flex flex-wrap gap-2">
            {filters.status !== 'all' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                Status: {filters.status}
                <button
                  onClick={() => updateFilter('status', 'all')}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            
            {filters.priority !== 'all' && (
              <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                Priority: {filters.priority}
                <button
                  onClick={() => updateFilter('priority', 'all')}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            
            {filters.subject && (
              <span className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
                Subject: {filters.subject}
                <button
                  onClick={() => updateFilter('subject', '')}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            )}
            
            {filters.tags.map((tag) => (
              <span
                key={tag}
                className="inline-flex items-center gap-1 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full"
              >
                Tag: {tag}
                <button
                  onClick={() => updateFilter('tags', filters.tags.filter(t => t !== tag))}
                  className="text-blue-600 hover:text-blue-800"
                >
                  <X className="w-3 h-3" />
                </button>
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

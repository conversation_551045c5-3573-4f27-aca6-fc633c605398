import React, { useState } from 'react';
import {
  CheckCircle2,
  Circle,
  Edit3,
  Trash2,
  Calendar,
  Clock,
  Tag,
  ChevronDown,
  ChevronRight,
  AlertTriangle,
  Repeat,
} from 'lucide-react';
import { Task } from '../../../types';
import {
  formatDueDate,
  getPriorityColor,
  getStatusColor,
  getTaskCompletionPercentage,
  isOverdue,
  isToday,
  isTomorrow,
} from '../utils/taskUtils';

interface TaskCardProps {
  task: Task;
  onEdit: (task: Task) => void;
  onDelete: (taskId: string) => void;
  onToggleStatus: (taskId: string) => void;
  onToggleSubtask?: (taskId: string, subtaskId: string) => void;
  className?: string;
}

export const TaskCard: React.FC<TaskCardProps> = ({
  task,
  onEdit,
  onDelete,
  onToggleStatus,
  onToggleSubtask,
  className = '',
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  const completionPercentage = getTaskCompletionPercentage(task);
  const hasSubtasks = task.subtasks && task.subtasks.length > 0;
  const overdue = isOverdue(task);
  const today = isToday(task);
  const tomorrow = isTomorrow(task);

  const handleToggleStatus = async () => {
    setIsAnimating(true);
    try {
      await onToggleStatus(task.id);
    } finally {
      setTimeout(() => setIsAnimating(false), 300);
    }
  };

  const handleToggleSubtask = (subtaskId: string) => {
    if (onToggleSubtask) {
      onToggleSubtask(task.id, subtaskId);
    }
  };

  const getDateBadge = () => {
    if (overdue) {
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-red-600 bg-red-50 border border-red-200 rounded-full">
          <AlertTriangle className="w-3 h-3" />
          Overdue
        </span>
      );
    }
    
    if (today) {
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 border border-blue-200 rounded-full">
          <Calendar className="w-3 h-3" />
          Today
        </span>
      );
    }
    
    if (tomorrow) {
      return (
        <span className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-orange-600 bg-orange-50 border border-orange-200 rounded-full">
          <Calendar className="w-3 h-3" />
          Tomorrow
        </span>
      );
    }
    
    return null;
  };

  return (
    <div
      className={`
        border rounded-lg p-4 transition-all duration-200 hover:shadow-md
        ${task.status === 'completed' ? 'bg-gray-50 border-gray-200' : 'bg-white'}
        ${overdue && task.status !== 'completed' ? 'border-red-200 bg-red-50' : 'border-gray-200'}
        ${isAnimating ? 'scale-95 opacity-75' : ''}
        ${className}
      `}
    >
      <div className="flex items-start gap-3">
        {/* Status Toggle */}
        <button
          onClick={handleToggleStatus}
          className="mt-1 text-blue-600 hover:text-blue-700 transition-colors"
          disabled={isAnimating}
        >
          {task.status === 'completed' ? (
            <CheckCircle2 className="w-5 h-5" />
          ) : (
            <Circle className="w-5 h-5" />
          )}
        </button>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          {/* Title and Actions */}
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <h3
                className={`font-medium text-gray-900 ${
                  task.status === 'completed' ? 'line-through text-gray-500' : ''
                }`}
              >
                {task.title}
              </h3>
              
              {task.description && (
                <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                  {task.description}
                </p>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-1">
              {hasSubtasks && (
                <button
                  onClick={() => setIsExpanded(!isExpanded)}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                >
                  {isExpanded ? (
                    <ChevronDown className="w-4 h-4" />
                  ) : (
                    <ChevronRight className="w-4 h-4" />
                  )}
                </button>
              )}
              
              <button
                onClick={() => onEdit(task)}
                className="p-1 text-gray-400 hover:text-blue-600 transition-colors"
              >
                <Edit3 className="w-4 h-4" />
              </button>
              
              <button
                onClick={() => onDelete(task.id)}
                className="p-1 text-gray-400 hover:text-red-600 transition-colors"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>

          {/* Metadata */}
          <div className="flex items-center gap-3 mt-3 text-sm">
            {/* Due Date */}
            <div className="flex items-center gap-1 text-gray-600">
              <Calendar className="w-4 h-4" />
              <span>{formatDueDate(task.dueDate)}</span>
            </div>

            {/* Priority */}
            <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(task.priority)}`}>
              {task.priority}
            </span>

            {/* Subject */}
            {task.subject && (
              <div className="flex items-center gap-1 text-gray-600">
                <Tag className="w-4 h-4" />
                <span>{task.subject}</span>
              </div>
            )}

            {/* Estimated Time */}
            {task.estimatedTime && (
              <div className="flex items-center gap-1 text-gray-600">
                <Clock className="w-4 h-4" />
                <span>{task.estimatedTime}m</span>
              </div>
            )}

            {/* Recurring */}
            {task.recurrence && (
              <div className="flex items-center gap-1 text-gray-600">
                <Repeat className="w-4 h-4" />
                <span className="text-xs">Recurring</span>
              </div>
            )}
          </div>

          {/* Tags */}
          {task.tags && task.tags.length > 0 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {task.tags.map((tag) => (
                <span
                  key={tag}
                  className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          )}

          {/* Date Badge */}
          <div className="mt-2">
            {getDateBadge()}
          </div>

          {/* Progress Bar for Subtasks */}
          {hasSubtasks && (
            <div className="mt-3">
              <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                <span>Progress</span>
                <span>{completionPercentage}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${completionPercentage}%` }}
                />
              </div>
            </div>
          )}

          {/* Subtasks */}
          {hasSubtasks && isExpanded && (
            <div className="mt-3 pl-4 border-l-2 border-gray-200">
              <div className="space-y-2">
                {task.subtasks!.map((subtask) => (
                  <div key={subtask.id} className="flex items-center gap-2">
                    <button
                      onClick={() => handleToggleSubtask(subtask.id)}
                      className="text-gray-400 hover:text-blue-600 transition-colors"
                    >
                      {subtask.completed ? (
                        <CheckCircle2 className="w-4 h-4" />
                      ) : (
                        <Circle className="w-4 h-4" />
                      )}
                    </button>
                    <span
                      className={`text-sm ${
                        subtask.completed
                          ? 'line-through text-gray-500'
                          : 'text-gray-700'
                      }`}
                    >
                      {subtask.title}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

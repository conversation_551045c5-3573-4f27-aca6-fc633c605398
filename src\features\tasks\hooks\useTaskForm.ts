import { useState, useCallback } from 'react';
import { TaskFormData, TaskPriority, SubTask, RecurrencePattern } from '../../../types';
import { validateTask } from '../utils/taskUtils';

export interface UseTaskFormReturn {
  formData: TaskFormData;
  errors: string[];
  isValid: boolean;
  updateField: <K extends keyof TaskFormData>(field: K, value: TaskFormData[K]) => void;
  addSubtask: (title: string) => void;
  removeSubtask: (id: string) => void;
  toggleSubtask: (id: string) => void;
  updateSubtask: (id: string, title: string) => void;
  setRecurrence: (recurrence: RecurrencePattern | null) => void;
  addTag: (tag: string) => void;
  removeTag: (tag: string) => void;
  resetForm: () => void;
  validateForm: () => boolean;
  setFormData: (data: Partial<TaskFormData>) => void;
}

const defaultFormData: TaskFormData = {
  title: '',
  description: '',
  subject: '',
  dueDate: new Date().toISOString().split('T')[0],
  priority: 'medium',
  subtasks: [],
  recurrence: null,
  tags: [],
  estimatedTime: undefined,
};

export function useTaskForm(initialData?: Partial<TaskFormData>): UseTaskFormReturn {
  const [formData, setFormDataState] = useState<TaskFormData>({
    ...defaultFormData,
    ...initialData,
  });
  const [errors, setErrors] = useState<string[]>([]);

  const updateField = useCallback(<K extends keyof TaskFormData>(
    field: K,
    value: TaskFormData[K]
  ) => {
    setFormDataState(prev => ({ ...prev, [field]: value }));
    // Clear errors when user starts typing
    if (errors.length > 0) {
      setErrors([]);
    }
  }, [errors.length]);

  const addSubtask = useCallback((title: string) => {
    if (!title.trim()) return;
    
    const newSubtask: SubTask = {
      id: `subtask_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      title: title.trim(),
      completed: false,
      createdAt: new Date().toISOString(),
    };

    setFormDataState(prev => ({
      ...prev,
      subtasks: [...prev.subtasks, newSubtask],
    }));
  }, []);

  const removeSubtask = useCallback((id: string) => {
    setFormDataState(prev => ({
      ...prev,
      subtasks: prev.subtasks.filter(st => st.id !== id),
    }));
  }, []);

  const toggleSubtask = useCallback((id: string) => {
    setFormDataState(prev => ({
      ...prev,
      subtasks: prev.subtasks.map(st =>
        st.id === id ? { ...st, completed: !st.completed } : st
      ),
    }));
  }, []);

  const updateSubtask = useCallback((id: string, title: string) => {
    setFormDataState(prev => ({
      ...prev,
      subtasks: prev.subtasks.map(st =>
        st.id === id ? { ...st, title: title.trim() } : st
      ),
    }));
  }, []);

  const setRecurrence = useCallback((recurrence: RecurrencePattern | null) => {
    setFormDataState(prev => ({ ...prev, recurrence }));
  }, []);

  const addTag = useCallback((tag: string) => {
    const trimmedTag = tag.trim();
    if (!trimmedTag || formData.tags.includes(trimmedTag)) return;

    setFormDataState(prev => ({
      ...prev,
      tags: [...prev.tags, trimmedTag],
    }));
  }, [formData.tags]);

  const removeTag = useCallback((tag: string) => {
    setFormDataState(prev => ({
      ...prev,
      tags: prev.tags.filter(t => t !== tag),
    }));
  }, []);

  const resetForm = useCallback(() => {
    setFormDataState(defaultFormData);
    setErrors([]);
  }, []);

  const validateForm = useCallback(() => {
    const validationErrors = validateTask(formData);
    setErrors(validationErrors);
    return validationErrors.length === 0;
  }, [formData]);

  const setFormData = useCallback((data: Partial<TaskFormData>) => {
    setFormDataState(prev => ({ ...prev, ...data }));
  }, []);

  const isValid = errors.length === 0 && formData.title.trim() !== '' && formData.dueDate !== '';

  return {
    formData,
    errors,
    isValid,
    updateField,
    addSubtask,
    removeSubtask,
    toggleSubtask,
    updateSubtask,
    setRecurrence,
    addTag,
    removeTag,
    resetForm,
    validateForm,
    setFormData,
  };
}

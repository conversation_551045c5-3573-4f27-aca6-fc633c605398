import { useState, useEffect, useCallback } from 'react';
import { Task, TaskFormData } from '../../../types';
import { taskService } from '../services/taskService';
import { realTimeAuth } from '../../../utils/realTimeAuth';

export interface UseTasksReturn {
  tasks: Task[];
  loading: boolean;
  error: string | null;
  createTask: (taskData: TaskFormData) => Promise<void>;
  updateTask: (taskId: string, updates: Partial<Task>) => Promise<void>;
  deleteTask: (taskId: string) => Promise<void>;
  toggleTaskStatus: (taskId: string) => Promise<void>;
  bulkUpdateTasks: (updates: Array<{ id: string; updates: Partial<Task> }>) => Promise<void>;
  refreshTasks: () => Promise<void>;
  clearError: () => void;
}

export function useTasks(): UseTasksReturn {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const user = realTimeAuth.getCurrentUser();

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const loadTasks = useCallback(async (showLoading = true) => {
    if (!user) {
      setTasks([]);
      setLoading(false);
      return;
    }

    try {
      if (showLoading) setLoading(true);
      setError(null);
      
      const userTasks = await taskService.getTasks(user.id);
      setTasks(userTasks);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load tasks';
      setError(errorMessage);
      console.error('Error loading tasks:', err);
    } finally {
      setLoading(false);
    }
  }, [user]);

  const createTask = useCallback(async (taskData: TaskFormData) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      setError(null);
      await taskService.createTask(user.id, taskData);
      await loadTasks(false); // Refresh without showing loading
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create task';
      setError(errorMessage);
      throw err;
    }
  }, [user, loadTasks]);

  const updateTask = useCallback(async (taskId: string, updates: Partial<Task>) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      setError(null);
      await taskService.updateTask(user.id, taskId, updates);
      await loadTasks(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update task';
      setError(errorMessage);
      throw err;
    }
  }, [user, loadTasks]);

  const deleteTask = useCallback(async (taskId: string) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      setError(null);
      await taskService.deleteTask(user.id, taskId);
      await loadTasks(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete task';
      setError(errorMessage);
      throw err;
    }
  }, [user, loadTasks]);

  const toggleTaskStatus = useCallback(async (taskId: string) => {
    const task = tasks.find(t => t.id === taskId);
    if (!task) {
      throw new Error('Task not found');
    }

    const newStatus = task.status === 'completed' ? 'pending' : 'completed';
    await updateTask(taskId, { status: newStatus });
  }, [tasks, updateTask]);

  const bulkUpdateTasks = useCallback(async (updates: Array<{ id: string; updates: Partial<Task> }>) => {
    if (!user) {
      throw new Error('User not authenticated');
    }

    try {
      setError(null);
      await taskService.bulkUpdateTasks(user.id, updates);
      await loadTasks(false);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update tasks';
      setError(errorMessage);
      throw err;
    }
  }, [user, loadTasks]);

  const refreshTasks = useCallback(async () => {
    await loadTasks(true);
  }, [loadTasks]);

  // Load tasks on mount and when user changes
  useEffect(() => {
    loadTasks();
  }, [loadTasks]);

  // Listen for auth state changes
  useEffect(() => {
    const unsubscribe = realTimeAuth.onAuthStateChanged((newUser) => {
      if (!newUser) {
        setTasks([]);
        setLoading(false);
        setError(null);
      } else {
        loadTasks();
      }
    });

    return unsubscribe;
  }, [loadTasks]);

  return {
    tasks,
    loading,
    error,
    createTask,
    updateTask,
    deleteTask,
    toggleTaskStatus,
    bulkUpdateTasks,
    refreshTasks,
    clearError,
  };
}

import { useState, useMemo, useCallback } from 'react';
import { Task, TaskFilters, TaskFilter, TaskPriority } from '../../../types';
import { isOverdue, isToday, isTomorrow, isThisWeek } from '../utils/taskUtils';

export interface UseTaskFiltersReturn {
  filters: TaskFilters;
  filteredTasks: Task[];
  updateFilter: <K extends keyof TaskFilters>(key: K, value: TaskFilters[K]) => void;
  clearFilters: () => void;
  setSearchQuery: (query: string) => void;
  getFilterCounts: () => Record<TaskFilter, number>;
}

const defaultFilters: TaskFilters = {
  status: 'all',
  priority: 'all',
  subject: '',
  dateRange: { startDate: '', endDate: '' },
  search: '',
  tags: [],
};

export function useTaskFilters(tasks: Task[]): UseTaskFiltersReturn {
  const [filters, setFilters] = useState<TaskFilters>(defaultFilters);

  const updateFilter = useCallback(<K extends keyof TaskFilters>(
    key: K,
    value: TaskFilters[K]
  ) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  }, []);

  const clearFilters = useCallback(() => {
    setFilters(defaultFilters);
  }, []);

  const setSearchQuery = useCallback((query: string) => {
    updateFilter('search', query);
  }, [updateFilter]);

  const filteredTasks = useMemo(() => {
    let filtered = [...tasks];

    // Filter by status
    if (filters.status !== 'all') {
      switch (filters.status) {
        case 'pending':
          filtered = filtered.filter(task => task.status === 'pending');
          break;
        case 'completed':
          filtered = filtered.filter(task => task.status === 'completed');
          break;
        case 'overdue':
          filtered = filtered.filter(task => isOverdue(task));
          break;
        case 'today':
          filtered = filtered.filter(task => isToday(task));
          break;
        case 'tomorrow':
          filtered = filtered.filter(task => isTomorrow(task));
          break;
        case 'this-week':
          filtered = filtered.filter(task => isThisWeek(task));
          break;
      }
    }

    // Filter by priority
    if (filters.priority !== 'all') {
      filtered = filtered.filter(task => task.priority === filters.priority);
    }

    // Filter by subject
    if (filters.subject) {
      filtered = filtered.filter(task => 
        task.subject.toLowerCase().includes(filters.subject.toLowerCase())
      );
    }

    // Filter by search query
    if (filters.search) {
      const searchLower = filters.search.toLowerCase();
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchLower) ||
        task.description.toLowerCase().includes(searchLower) ||
        task.subject.toLowerCase().includes(searchLower) ||
        (task.tags && task.tags.some(tag => tag.toLowerCase().includes(searchLower)))
      );
    }

    // Filter by tags
    if (filters.tags.length > 0) {
      filtered = filtered.filter(task =>
        task.tags && filters.tags.every(filterTag =>
          task.tags.some(taskTag => taskTag.toLowerCase() === filterTag.toLowerCase())
        )
      );
    }

    // Filter by date range
    if (filters.dateRange.startDate || filters.dateRange.endDate) {
      filtered = filtered.filter(task => {
        const taskDate = new Date(task.dueDate);
        const startDate = filters.dateRange.startDate ? new Date(filters.dateRange.startDate) : null;
        const endDate = filters.dateRange.endDate ? new Date(filters.dateRange.endDate) : null;

        if (startDate && taskDate < startDate) return false;
        if (endDate && taskDate > endDate) return false;
        return true;
      });
    }

    return filtered;
  }, [tasks, filters]);

  const getFilterCounts = useCallback((): Record<TaskFilter, number> => {
    return {
      all: tasks.length,
      pending: tasks.filter(task => task.status === 'pending').length,
      completed: tasks.filter(task => task.status === 'completed').length,
      overdue: tasks.filter(task => isOverdue(task)).length,
      today: tasks.filter(task => isToday(task)).length,
      tomorrow: tasks.filter(task => isTomorrow(task)).length,
      'this-week': tasks.filter(task => isThisWeek(task)).length,
    };
  }, [tasks]);

  return {
    filters,
    filteredTasks,
    updateFilter,
    clearFilters,
    setSearchQuery,
    getFilterCounts,
  };
}
